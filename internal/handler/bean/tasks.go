package bean

type SubscribeMessageTaskReq struct {
	ID          int32                  `json:"id"` // = sdasdasd id
	AccessToken string                 `json:"access_token"`
	OpenID      string                 `json:"open_id"`
	TemplateID  string                 `json:"template_id"`
	Page        string                 `json:"page"`
	Data        map[string]interface{} `json:"data"`

	// 用户ID映射回退信息 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	OriginalUserID string `json:"original_user_id,omitempty"` // 原始用户ID（用于回退）
	OriginalGameID string `json:"original_game_id,omitempty"` // 原始游戏ID（用于回退）
	IsMapped       bool   `json:"is_mapped,omitempty"`        // 是否进行了用户ID映射
}

type CustomerMessageTaskReq struct {
	AccessToken string                 `json:"access_token"`
	OpenID      string                 `json:"open_id"`
	MsgType     string                 `json:"msg_type"`
	Data        map[string]interface{} `json:"data"`
}

type DouyinPayTaskReq struct {
	GameID         string `json:"game_id"`      // 游戏ID，用于重新获取最新配置
	AccessToken    string `json:"access_token"` // 废弃：改为通过 GameID 实时获取
	OpenID         string `json:"open_id"`
	PaySecret      string `json:"pay_secret"` // 废弃：改为通过 GameID 实时获取
	AppID          string `json:"app_id"`     // 废弃：改为通过 GameID 实时获取
	OrderID        string `json:"order_id"`
	OrderPlatform  string `json:"order_platform"`
	SaveAmt        int32  `json:"save_amt"`
	DeductAmt      int32  `json:"deduct_amt"`
	IsSmallDiamond bool   `json:"is_small_diamond"` // 是否为小额钻石支付
}

type ShipmentTask struct {
	Attempt     int    `json:"attempt"`
	OrderID     string `json:"order_id"`
	GameID      string `json:"game_id"`
	UserID      string `json:"user_id"`
	CallbackURL string `json:"callback_url"`
}
