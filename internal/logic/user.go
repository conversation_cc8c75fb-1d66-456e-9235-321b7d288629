package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/internal/utils"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

var (
	_userOnce  sync.Once
	_userLogic *UserLogic
)

type UserLogic struct {
	userService              *service.UserService
	secretService            *service.SecretService
	subscribeService         *service.SubscribeService
	minigameService          *service.MinigameService
	dataReportService        *service.DataReportService
	douyinService            *service.DouyinService
	wechatPayService         *service.WechatPayService
	qqService                *service.QQService
	httpsWechatService       *https.WechatHttpService
	stopServiceConfigService *service.StopServiceConfigService
}

func SingletonUserLogic() *UserLogic {
	_userOnce.Do(func() {
		_userLogic = &UserLogic{
			userService:              service.SingletonUserService(),
			secretService:            service.SingletonSecretService(),
			subscribeService:         service.SingletonSubscribeService(),
			minigameService:          service.SingletonMinigameService(),
			dataReportService:        service.SingletonDataReportService(),
			douyinService:            service.SingletonDouyinService(),
			wechatPayService:         service.SingletonWechatPayService(),
			qqService:                service.SingletonQQService(),
			httpsWechatService:       https.SingletonWechatHttpService(),
			stopServiceConfigService: service.SingletonStopServiceConfigService(),
		}
	})
	return _userLogic
}

// Heartbeat 心跳检测
func (l *UserLogic) Heartbeat(ctx context.Context, req *bean.HeartbeatReq) error {
	return l.userService.Heartbeat(ctx, req.Action)
}

// GetTimestamp 获取时间
func (l *UserLogic) GetTimestamp(ctx context.Context) (*bean.TimeRes, error) {
	return l.userService.GetTimestamp(ctx)
}

// Login 登录
func (l *UserLogic) Login(ctx context.Context, req *bean.LoginReq) (*bean.LoginRes, string, error) {
	start := time.Now()
	defer func() {
		elapsed := time.Since(start)
		latencyMillis := float64(elapsed) / float64(time.Millisecond)
		fields := make(map[string]interface{})
		fields["game_id"] = req.GameID
		fields["latency_millis"] = fmt.Sprintf("%.3f", latencyMillis)
		fields["path"] = "/admin-console/login"
		fields["method"] = "POST"
		logger.Logger.InfoWithFiledCtx(ctx, fields, "接口耗时")
	}()

	// 先检查Redis缓存中的游戏存在性
	gameExistCacheKey := fmt.Sprintf(constants.RedisGameExistKey, req.GameID)
	gameExists, err := redis.Get(ctx, gameExistCacheKey)
	if err == nil {
		// 缓存命中，使用tagged switch处理不同的缓存值
		switch gameExists {
		case "true":
			// 游戏存在，继续执行
		case "false":
			// 游戏不存在（缓存的否定结果）
			return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
		default:
			// 缓存值异常，清除缓存并查询数据库
			logger.Logger.WarnfCtx(ctx, "[Login] 游戏存在性缓存值异常: %s, 清除缓存", gameExists)
			redis.Redis().Del(ctx, gameExistCacheKey)
			// 继续执行数据库查询逻辑
		}
	} else {
		// 缓存未命中，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一游戏的并发查询
		lockKey := constants.RedisGameExistLockKeyPrefix + req.GameID
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, gameExistCacheKey); doubleCheckErr == nil {
				switch doubleCheckValue {
				case "true":
					// 继续执行后续逻辑
				case "false":
					// 游戏不存在（缓存的否定结果）
					return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
				}
			}

			// 执行数据库查询
			_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)

			// 缓存查询结果（包括否定结果）
			cacheValue := "false"
			if err == nil {
				cacheValue = "true"
			}

			// 将结果缓存到Redis，使用常量定义的TTL
			if cacheErr := redis.Set(ctx, gameExistCacheKey, cacheValue, time.Duration(constants.RedisGameExistExpire)*time.Second); cacheErr != nil {
				// 缓存失败不影响主流程，只记录日志
				logger.Logger.WarnfCtx(ctx, "[Login] 缓存游戏存在性失败: %v", cacheErr)
			}

			if err != nil {
				return nil, "", err
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, gameExistCacheKey); retryErr == nil {
				switch retryValue {
				case "true":
					// 继续执行后续逻辑
				case "false":
					// 游戏不存在（缓存的否定结果）
					return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
				}
			}

			// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
			logger.Logger.WarnfCtx(ctx, "[Login] 获取游戏存在性锁失败，降级为直接查询, gameID: %s", req.GameID)
			_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)
			if err != nil {
				return nil, "", err
			}
		}
	}

	if req.Channel == "" {
		req.Channel = constants.ThinkingdataDefault
	}
	if req.ADFrom == "" {
		req.ADFrom = constants.ThinkingdataDefault
	}

	config, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	if config == nil {
		return nil, "", fmt.Errorf("Login minigame config is empty, game_id: %s", req.GameID)
	}

	if config.DisableInsecure == constants.TrueInt {
		logger.Logger.WarnfCtx(ctx, "Login minigame config is disable, game_id: %s, code: %s, channel: %s, ad_from: %s", req.GameID, req.Code, req.Channel, req.ADFrom)
		return nil, "", constants.ErrLoginConfigIsDisable
	}

	// 根据客户端code获取Openid
	logger.Logger.InfofWithFiledCtx(ctx, map[string]interface{}{
		"game_id":   req.GameID,
		"wc_app_id": config.AppID,
		"code":      req.Code,
	}, "Login req api:jscode2session")

	// logger.Logger.InfofCtx(ctx, "Login req api:jscode2session, game_id: %s, wc_app_id: %s, code: %s", req.GameID, config.AppID, req.Code)
	miniGameSession, err := l.minigameService.GetCode2Session(ctx, config.AppID, config.AppSercet, req.Code)
	if err != nil {
		return nil, "", err
	}
	logger.Logger.InfofCtx(ctx, "Login channel: %s,  ad_from: %s", req.Channel, req.ADFrom)

	// 在创建用户前，先检查是否为新用户，以及是否允许新用户注册（使用带缓存和分布式锁的优化版本）
	isNewUser, err := l.userService.IsNewUserWithCache(ctx, miniGameSession.OpenID, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[Login] 检查是否为新用户失败, game_id: %s, open_id: %s, error: %v", req.GameID, miniGameSession.OpenID, err)
		return nil, "", err
	}

	if isNewUser {
		// 如果是新用户，先检查是否允许注册
		logger.Logger.InfofCtx(ctx, "[Login] 检测到新用户，开始检查停服配置, game_id: %s, platform: minigame, open_id: %s", req.GameID, miniGameSession.OpenID)
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeMinigame)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[Login] 检查新用户注册配置失败, game_id: %s, platform: minigame, error: %v", req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[Login] 该游戏已关闭新用户注册, game_id: %s, platform: minigame, open_id: %s", req.GameID, miniGameSession.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
		logger.Logger.InfofCtx(ctx, "[Login] 停服配置检查通过，允许新用户注册, game_id: %s, platform: minigame, open_id: %s", req.GameID, miniGameSession.OpenID)
	}

	// 优化：使用缓存获取或创建用户信息

	// 先尝试从Redis缓存获取用户信息（仅对已存在用户）
	userCacheKey := fmt.Sprintf(constants.RedisUserInfoKey, miniGameSession.OpenID)
	cachedUser, err := redis.Get(ctx, userCacheKey)

	var user *bean.User
	if err == nil && cachedUser != "" {
		// 缓存命中，反序列化用户信息
		user = &bean.User{}
		if jsonErr := json.Unmarshal([]byte(cachedUser), user); jsonErr == nil {
			// 记录缓存命中监控日志
			fields := map[string]interface{}{
				"operation": "user_info_cache_hit",
				"open_id":   miniGameSession.OpenID,
				"cache_key": userCacheKey,
				"data_size": len(cachedUser),
			}
			logger.Logger.InfoWithFiledCtx(ctx, fields, "用户信息缓存命中")
		} else {
			// 缓存数据损坏，清除缓存并查询数据库
			redis.Redis().Del(ctx, userCacheKey)
			user = nil
			// 记录缓存损坏监控日志
			logger.Logger.WarnfCtx(ctx, "[Login] 用户信息缓存数据损坏, openID: %s, error: %v", miniGameSession.OpenID, jsonErr)
		}
	}

	if user == nil {
		// 缓存未命中或损坏，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一用户信息的并发查询
		lockKey := fmt.Sprintf("cache:user_info_lock:%s:%s", req.GameID, miniGameSession.OpenID)
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, userCacheKey); doubleCheckErr == nil && doubleCheckValue != "" {
				user = &bean.User{}
				if jsonErr := json.Unmarshal([]byte(doubleCheckValue), user); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					// 缓存数据损坏，清除缓存
					redis.Redis().Del(ctx, userCacheKey)
					user = nil
				}
			}

			if user == nil {
				// 执行数据库查询
				user, err = l.userService.GetUserInfoByOpenIDOptimized(ctx, req.Code, req.GameID, req.Channel, req.ADFrom,
					miniGameSession.OpenID, miniGameSession.UnionID, miniGameSession.SessionKey)
				if err != nil {
					return nil, "", err
				}

				// 缓存策略优化：Write-Through模式 - 统一缓存策略
				cacheManager := utils.NewCacheManager()
				if cacheErr := cacheManager.UpdateUserCache(ctx, miniGameSession.OpenID, user); cacheErr != nil {
					logger.Logger.WarnfCtx(ctx, "[Login] 更新用户缓存失败: %v", cacheErr)
				}

				if user.IsRegister {
					// 新注册用户：同时更新IsNewUser缓存状态，确保缓存一致性
					// 注册完成后用户已存在，所以userIsNew=false
					l.userService.UpdateUserExistsCache(ctx, miniGameSession.OpenID, req.GameID, false)
					logger.Logger.InfofCtx(ctx, "[Login] 新用户注册成功，已更新用户信息和存在性缓存, userID: %s, gameID: %s, openID: %s",
						user.UserID, req.GameID, miniGameSession.OpenID)
				}
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, userCacheKey); retryErr == nil && retryValue != "" {
				user = &bean.User{}
				if jsonErr := json.Unmarshal([]byte(retryValue), user); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					user = nil
				}
			}

			if user == nil {
				// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
				logger.Logger.WarnfCtx(ctx, "[Login] 获取用户信息锁失败，降级为直接查询, gameID: %s, openID: %s", req.GameID, miniGameSession.OpenID)
				user, err = l.userService.GetUserInfoByOpenIDOptimized(ctx, req.Code, req.GameID, req.Channel, req.ADFrom,
					miniGameSession.OpenID, miniGameSession.UnionID, miniGameSession.SessionKey)
				if err != nil {
					return nil, "", err
				}
			}
		}
	}

	// 保存原始用户ID，用于token生成和日志记录
	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	mappedUserID, tokenUserID := l.ApplyUserIDMappingForKof(ctx, user, req.GameID, miniGameSession.UnionID, "Login")
	logger.Logger.InfofCtx(ctx, "Login user id: %s (token使用ID: %s)", mappedUserID, tokenUserID)

	user.DeviceID = req.DeviceID

	// 重新验证新用户状态，确保缓存一致性
	// 如果用户是通过数据库查询获取的新注册用户，需要重新检查停服配置
	if user.IsRegister && !isNewUser {
		// 发现数据不一致：缓存说是老用户，但数据库显示是新注册
		logger.Logger.WarnfCtx(ctx, "[Login] 发现用户状态不一致, 缓存显示老用户但实际是新注册, gameID: %s, openID: %s",
			req.GameID, miniGameSession.OpenID)

		// 重新检查停服配置
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeMinigame)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[Login] 重新检查新用户注册配置失败, game_id: %s, platform: minigame, error: %v",
				req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[Login] 重新检查发现该游戏已关闭新用户注册, game_id: %s, platform: minigame, open_id: %s",
				req.GameID, miniGameSession.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
	}

	// 是否封号
	ban, err := l.userService.CheckUserBan(ctx, user.UserID, req.GameID)
	if err != nil {
		logger.Logger.Errorf("CheckUserBan err: %s", err.Error())
		return nil, "", err
	}
	if ban {
		logger.Logger.WarnfCtx(ctx, "login user is banned, user_id: %s, game_id: %s", user.UserID, req.GameID)
		return nil, "", constants.ErrUserBan
	}

	// 先尝试从Redis缓存获取游戏信息
	gameInfoCacheKey := fmt.Sprintf(constants.RedisGameInfoKey, req.GameID)
	cachedGameInfo, err := redis.Get(ctx, gameInfoCacheKey)

	var gameInfo *model.MGame
	if err == nil && cachedGameInfo != "" {
		// 缓存命中，反序列化游戏信息
		gameInfo = &model.MGame{}
		if jsonErr := json.Unmarshal([]byte(cachedGameInfo), gameInfo); jsonErr == nil {
			// 缓存命中成功
		} else {
			// 缓存数据损坏，清除缓存并查询数据库
			redis.Redis().Del(ctx, gameInfoCacheKey)
			gameInfo = nil
		}
	}

	if gameInfo == nil {
		// 缓存未命中或损坏，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一游戏信息的并发查询
		lockKey := constants.RedisGameInfoLockKeyPrefix + req.GameID
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, gameInfoCacheKey); doubleCheckErr == nil && doubleCheckValue != "" {
				gameInfo = &model.MGame{}
				if jsonErr := json.Unmarshal([]byte(doubleCheckValue), gameInfo); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					// 缓存数据损坏，清除缓存
					redis.Redis().Del(ctx, gameInfoCacheKey)
					gameInfo = nil
				}
			}

			if gameInfo == nil {
				// 执行数据库查询
				gameInfo, err = l.userService.GetGameInfo(ctx, req.GameID)
				if err != nil {
					return nil, "", err
				}

				// 将游戏信息缓存到Redis，使用常量定义的TTL
				if gameInfoData, jsonErr := json.Marshal(gameInfo); jsonErr == nil {
					if cacheErr := redis.Set(ctx, gameInfoCacheKey, string(gameInfoData), time.Duration(constants.RedisGameInfoExpire)*time.Second); cacheErr != nil {
						// 缓存失败不影响主流程，只记录日志
						logger.Logger.WarnfCtx(ctx, "[Login] 缓存游戏信息失败: %v", cacheErr)
					}
				}
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, gameInfoCacheKey); retryErr == nil && retryValue != "" {
				gameInfo = &model.MGame{}
				if jsonErr := json.Unmarshal([]byte(retryValue), gameInfo); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					gameInfo = nil
				}
			}

			if gameInfo == nil {
				// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
				logger.Logger.WarnfCtx(ctx, "[Login] 获取游戏信息锁失败，降级为直接查询, gameID: %s", req.GameID)
				gameInfo, err = l.userService.GetGameInfo(ctx, req.GameID)
				if err != nil {
					return nil, "", err
				}
			}
		}
	}

	// 如果是新用户注册，检查是否禁止新用户注册
	if user.IsRegister {
		// 停服检查已经通过，进行注册数据上报
		if err := l.dataReportService.ReportUserRegister(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, miniGameSession.OpenID, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(ctx, "Login ReportUserRegister err: %s", err.Error())
		}
		if err := l.dataReportService.ReportRegister(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, req.OS, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(ctx, "Login ReportRegister err: %s", err.Error())
		}
	}

	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, user.UserInfo)
	if err != nil {
		return nil, "", err
	}

	// 下发jwt - 使用原始用户ID生成token
	logger.Logger.InfofCtx(ctx, "Login user info (token使用ID): %s | %s | %s | %s | %s | %s", mappedUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	token, err := util.GenerateToken(mappedUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(mappedUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS) // TODO tokenUserID -> mapping
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	// 打点登录或注册 - 使用原始用户ID
	if err := l.dataReportService.ReportUserLogin(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, miniGameSession.OpenID, user.RegisterAt, user.ChannelInfo); err != nil {
		logger.Logger.ErrorfCtx(ctx, "Login ReportUserLogin err: %s", err.Error())
	}

	if err := l.dataReportService.ReportLogin(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, user.ChannelInfo); err != nil {
		logger.Logger.ErrorfCtx(ctx, "Login ReportLogin err: %s", err.Error())
	}

	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  user.ChannelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

// LoginV2 登录v2
func (l *UserLogic) LoginV2(ctx context.Context, req *bean.LoginV2Req) (*bean.LoginRes, string, error) {
	start := time.Now()
	defer func() {
		elapsed := time.Since(start)
		latencyMillis := float64(elapsed) / float64(time.Millisecond)
		fields := make(map[string]interface{})
		fields["game_id"] = req.GameID
		fields["latency_millis"] = fmt.Sprintf("%.3f", latencyMillis)
		fields["path"] = "/admin-console/v2/login"
		fields["method"] = "POST"
		logger.Logger.InfoWithFiledCtx(ctx, fields, "接口耗时")
	}()

	// 先检查Redis缓存中的游戏存在性
	gameExistCacheKey := fmt.Sprintf(constants.RedisGameExistKey, req.GameID)
	gameExists, err := redis.Get(ctx, gameExistCacheKey)
	if err == nil {
		// 缓存命中，使用tagged switch处理不同的缓存值
		switch gameExists {
		case "true":
			// 游戏存在，继续执行
		case "false":
			// 游戏不存在（缓存的否定结果）
			return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
		default:
			// 缓存值异常，清除缓存并查询数据库
			logger.Logger.WarnfCtx(ctx, "[LoginV2] 游戏存在性缓存值异常: %s, 清除缓存", gameExists)
			redis.Redis().Del(ctx, gameExistCacheKey)
			// 继续执行数据库查询逻辑
		}
	} else {
		// 缓存未命中，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一游戏的并发查询
		lockKey := constants.RedisGameExistLockKeyPrefix + req.GameID
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, gameExistCacheKey); doubleCheckErr == nil {
				switch doubleCheckValue {
				case "true":
					// 继续执行后续逻辑
				case "false":
					// 游戏不存在（缓存的否定结果）
					return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
				}
			}

			// 执行数据库查询
			_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)

			// 缓存查询结果（包括否定结果）
			cacheValue := "false"
			if err == nil {
				cacheValue = "true"
			}

			// 将结果缓存到Redis，使用常量定义的TTL
			if cacheErr := redis.Set(ctx, gameExistCacheKey, cacheValue, time.Duration(constants.RedisGameExistExpire)*time.Second); cacheErr != nil {
				// 缓存失败不影响主流程，只记录日志
				logger.Logger.WarnfCtx(ctx, "[LoginV2] 缓存游戏存在性失败: %v", cacheErr)
			}

			if err != nil {
				return nil, "", err
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, gameExistCacheKey); retryErr == nil {
				switch retryValue {
				case "true":
					// 继续执行后续逻辑
				case "false":
					// 游戏不存在（缓存的否定结果）
					return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
				}
			}

			// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
			logger.Logger.WarnfCtx(ctx, "[LoginV2] 获取游戏存在性锁失败，降级为直接查询, gameID: %s", req.GameID)
			_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)
			if err != nil {
				return nil, "", err
			}
		}
	}

	// 参数标准化
	if req.Channel == "" {
		req.Channel = constants.ThinkingdataDefault
	}
	if req.ADFrom == "" {
		req.ADFrom = constants.ThinkingdataDefault
	}

	config, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	if config == nil {
		return nil, "", fmt.Errorf("Login minigame config is empty, game_id: %s", req.GameID)
	}
	// 根据客户端code获取Openid
	logger.Logger.InfofCtx(ctx, "Login req api:jscode2session, game_id: %s, wc_app_id: %s, code: %s", req.GameID, config.AppID, req.Code)
	miniGameSession, err := l.minigameService.GetCode2Session(ctx, config.AppID, config.AppSercet, req.Code)
	if err != nil {
		return nil, "", err
	}
	logger.Logger.InfofCtx(ctx, "Login channel: %s,  ad_from: %s", req.Channel, req.ADFrom)

	// 在创建用户前，先检查是否为新用户，以及是否允许新用户注册（使用带缓存和分布式锁的优化版本）
	isNewUser, err := l.userService.IsNewUserWithCache(ctx, miniGameSession.OpenID, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[LoginV2] 检查是否为新用户失败, game_id: %s, open_id: %s, error: %v", req.GameID, miniGameSession.OpenID, err)
		return nil, "", err
	}

	if isNewUser {
		// 如果是新用户，先检查是否允许注册
		logger.Logger.InfofCtx(ctx, "[LoginV2] 检测到新用户，开始检查停服配置, game_id: %s, platform: minigame, open_id: %s", req.GameID, miniGameSession.OpenID)
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeMinigame)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginV2] 检查新用户注册配置失败, game_id: %s, platform: minigame, error: %v", req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[LoginV2] 该游戏已关闭新用户注册, game_id: %s, platform: minigame, open_id: %s", req.GameID, miniGameSession.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
		logger.Logger.InfofCtx(ctx, "[LoginV2] 停服配置检查通过，允许新用户注册, game_id: %s, platform: minigame, open_id: %s", req.GameID, miniGameSession.OpenID)
	}

	// 先尝试从Redis缓存获取用户信息（仅对已存在用户）
	userCacheKey := fmt.Sprintf(constants.RedisUserInfoKey, miniGameSession.OpenID)
	cachedUser, err := redis.Get(ctx, userCacheKey)

	var user *bean.User
	if err == nil && cachedUser != "" {
		// 缓存命中，反序列化用户信息
		user = &bean.User{}
		if jsonErr := json.Unmarshal([]byte(cachedUser), user); jsonErr == nil {
			// 记录缓存命中监控日志
			fields := map[string]interface{}{
				"operation": "user_info_cache_hit",
				"open_id":   miniGameSession.OpenID,
				"cache_key": userCacheKey,
				"data_size": len(cachedUser),
			}
			logger.Logger.InfoWithFiledCtx(ctx, fields, "用户信息缓存命中")
		} else {
			// 缓存数据损坏，清除缓存并查询数据库
			redis.Redis().Del(ctx, userCacheKey)
			user = nil
			// 记录缓存损坏监控日志
			logger.Logger.WarnfCtx(ctx, "[LoginV2] 用户信息缓存数据损坏, openID: %s, error: %v", miniGameSession.OpenID, jsonErr)
		}
	}

	if user == nil {
		// 缓存未命中或损坏，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一用户信息的并发查询
		lockKey := fmt.Sprintf("cache:user_info_lock:%s:%s", req.GameID, miniGameSession.OpenID)
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, userCacheKey); doubleCheckErr == nil && doubleCheckValue != "" {
				user = &bean.User{}
				if jsonErr := json.Unmarshal([]byte(doubleCheckValue), user); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					// 缓存数据损坏，清除缓存
					redis.Redis().Del(ctx, userCacheKey)
					user = nil
				}
			}

			if user == nil {
				// 执行数据库查询
				user, err = l.userService.GetUserInfoByOpenIDOptimized(ctx, req.Code, req.GameID, req.Channel, req.ADFrom,
					miniGameSession.OpenID, miniGameSession.UnionID, miniGameSession.SessionKey)
				if err != nil {
					return nil, "", err
				}

				// 缓存策略优化：Write-Through模式 - 统一缓存策略
				cacheManager := utils.NewCacheManager()
				if cacheErr := cacheManager.UpdateUserCache(ctx, miniGameSession.OpenID, user); cacheErr != nil {
					logger.Logger.WarnfCtx(ctx, "[LoginV2] 更新用户缓存失败: %v", cacheErr)
				}

				if user.IsRegister {
					// 新注册用户：同时更新IsNewUser缓存状态，确保缓存一致性
					// 注册完成后用户已存在，所以userIsNew=false
					l.userService.UpdateUserExistsCache(ctx, miniGameSession.OpenID, req.GameID, false)
					logger.Logger.InfofCtx(ctx, "[LoginV2] 新用户注册成功，已更新用户信息和存在性缓存, userID: %s, gameID: %s, openID: %s",
						user.UserID, req.GameID, miniGameSession.OpenID)
				}
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, userCacheKey); retryErr == nil && retryValue != "" {
				user = &bean.User{}
				if jsonErr := json.Unmarshal([]byte(retryValue), user); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					user = nil
				}
			}

			if user == nil {
				// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
				logger.Logger.WarnfCtx(ctx, "[LoginV2] 获取用户信息锁失败，降级为直接查询, gameID: %s, openID: %s", req.GameID, miniGameSession.OpenID)
				user, err = l.userService.GetUserInfoByOpenIDOptimized(ctx, req.Code, req.GameID, req.Channel, req.ADFrom,
					miniGameSession.OpenID, miniGameSession.UnionID, miniGameSession.SessionKey)
				if err != nil {
					return nil, "", err
				}
			}
		}
	}

	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	var mappedUserID, tokenUserID string
	mappedUserID, tokenUserID = l.ApplyUserIDMappingForKof(ctx, user, req.GameID, miniGameSession.UnionID, "LoginV2")

	logger.Logger.InfofCtx(ctx, "LoginV2 user id: %s (token使用ID: %s)", mappedUserID, tokenUserID)
	user.DeviceID = req.DeviceID

	// 重新验证新用户状态，确保缓存一致性
	// 如果用户是通过数据库查询获取的新注册用户，需要重新检查停服配置
	if user.IsRegister && !isNewUser {
		// 发现数据不一致：缓存说是老用户，但数据库显示是新注册
		logger.Logger.WarnfCtx(ctx, "[LoginV2] 发现用户状态不一致, 缓存显示老用户但实际是新注册, gameID: %s, openID: %s",
			req.GameID, miniGameSession.OpenID)

		// 重新检查停服配置
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeMinigame)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginV2] 重新检查新用户注册配置失败, game_id: %s, platform: minigame, error: %v",
				req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[LoginV2] 重新检查发现该游戏已关闭新用户注册, game_id: %s, platform: minigame, open_id: %s",
				req.GameID, miniGameSession.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
	}

	// 是否封号
	ban, err := l.userService.CheckUserBan(ctx, user.UserID, req.GameID)
	if err != nil {
		logger.Logger.Errorf("CheckUserBan err: %s", err.Error())
		return nil, "", err
	}
	if ban {
		logger.Logger.WarnfCtx(ctx, "login user is banned, user_id: %s, game_id: %s", user.UserID, req.GameID)
		return nil, "", constants.ErrUserBan
	}

	// 优化：使用缓存获取游戏信息

	// 先尝试从Redis缓存获取游戏信息
	gameInfoCacheKey := fmt.Sprintf(constants.RedisGameInfoKey, req.GameID)
	cachedGameInfo, err := redis.Get(ctx, gameInfoCacheKey)

	var gameInfo *model.MGame
	if err == nil && cachedGameInfo != "" {
		// 缓存命中，反序列化游戏信息
		gameInfo = &model.MGame{}
		if jsonErr := json.Unmarshal([]byte(cachedGameInfo), gameInfo); jsonErr == nil {
			// 缓存命中成功
		} else {
			// 缓存数据损坏，清除缓存并查询数据库
			redis.Redis().Del(ctx, gameInfoCacheKey)
			gameInfo = nil
		}
	}

	if gameInfo == nil {
		// 缓存未命中或损坏，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一游戏信息的并发查询
		lockKey := constants.RedisGameInfoLockKeyPrefix + req.GameID
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, gameInfoCacheKey); doubleCheckErr == nil && doubleCheckValue != "" {
				gameInfo = &model.MGame{}
				if jsonErr := json.Unmarshal([]byte(doubleCheckValue), gameInfo); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					// 缓存数据损坏，清除缓存
					redis.Redis().Del(ctx, gameInfoCacheKey)
					gameInfo = nil
				}
			}

			if gameInfo == nil {
				// 执行数据库查询
				gameInfo, err = l.userService.GetGameInfo(ctx, req.GameID)
				if err != nil {
					return nil, "", err
				}

				// 将游戏信息缓存到Redis，使用常量定义的TTL
				if gameInfoData, jsonErr := json.Marshal(gameInfo); jsonErr == nil {
					if cacheErr := redis.Set(ctx, gameInfoCacheKey, string(gameInfoData), time.Duration(constants.RedisGameInfoExpire)*time.Second); cacheErr != nil {
						// 缓存失败不影响主流程，只记录日志
						logger.Logger.WarnfCtx(ctx, "[LoginV2] 缓存游戏信息失败: %v", cacheErr)
					}
				}
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, gameInfoCacheKey); retryErr == nil && retryValue != "" {
				gameInfo = &model.MGame{}
				if jsonErr := json.Unmarshal([]byte(retryValue), gameInfo); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					gameInfo = nil
				}
			}

			if gameInfo == nil {
				// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
				logger.Logger.WarnfCtx(ctx, "[LoginV2] 获取游戏信息锁失败，降级为直接查询, gameID: %s", req.GameID)
				gameInfo, err = l.userService.GetGameInfo(ctx, req.GameID)
				if err != nil {
					return nil, "", err
				}
			}
		}
	}

	// 如果是新用户注册，进行注册相关处理
	if user.IsRegister {
		// 停服检查已经通过，进行注册数据上报
		if err := l.dataReportService.ReportUserRegister(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, miniGameSession.OpenID, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(ctx, "LoginV2 ReportUserRegister err: %s", err.Error())
		}
		if err := l.dataReportService.ReportRegister(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, req.OS, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(ctx, "LoginV2 ReportRegister err: %s", err.Error())
		}
	}

	// 解密
	encryptKey, err := l.GetLastUserKeyByOpenID(ctx, req.GameID, miniGameSession.OpenID, req.Version)
	if err != nil {
		return nil, "", err
	}
	decryptData, err := l.secretService.DecryptData(encryptKey, req.EncryptData, req.IV)
	if err != nil {
		return nil, "", err
	}
	logger.Logger.InfofCtx(ctx, "decryptData: %s", decryptData)
	logger.Logger.InfofCtx(ctx, "encryptKey: %s", encryptKey)
	logger.Logger.InfofCtx(ctx, "req.IV: %s", req.IV)
	logger.Logger.InfofCtx(ctx, "util.EncodeMD5(req.IV+encryptKey): %s", util.EncodeMD5(req.IV+encryptKey))
	if decryptData != util.EncodeMD5(req.IV+encryptKey) {
		return nil, "", fmt.Errorf("decrypt data error, game_id: %s, open_id: %s", req.GameID, miniGameSession.OpenID)
	}

	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, user.UserInfo)
	if err != nil {
		return nil, "", err
	}

	// 下发jwt - 使用原始用户ID生成token
	logger.Logger.InfofCtx(ctx, "LoginV2 user info (token使用ID): %s | %s | %s | %s | %s | %s", tokenUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	token, err := util.GenerateToken(mappedUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(mappedUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	// 打点登录 - 如果发生置换，使用置换后的用户ID
	if err := l.dataReportService.ReportUserLogin(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, miniGameSession.OpenID, user.RegisterAt, user.ChannelInfo); err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoginV2 ReportUserLogin err: %s", err.Error())
	}
	if err := l.dataReportService.ReportLogin(ctx, mappedUserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, user.ChannelInfo); err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoginV2 ReportLogin err: %s", err.Error())
	}

	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  user.ChannelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

// LoginV2Test 测试专用登录接口，用于压力测试 - 优化版本
func (l *UserLogic) LoginV2Test(ctx context.Context, req *bean.LoginV2TestReq) (*bean.LoginRes, string, error) {
	// 临时token防护
	if req.TestToken != "bkx-3810e4ed-8a77-41e6-8c97-54dbb832ee8e" {
		return nil, "", nil
	}

	start := time.Now()

	// 性能监控指标
	cacheHitCount := 0
	cacheMissCount := 0
	lockAcquiredCount := 0
	lockFailedCount := 0

	defer func() {
		elapsed := time.Since(start)
		latencyMillis := float64(elapsed) / float64(time.Millisecond)
		fields := make(map[string]interface{})
		fields["game_id"] = req.GameID
		fields["latency_millis"] = fmt.Sprintf("%.3f", latencyMillis)
		fields["path"] = "/admin-console/v2/test/login"
		fields["method"] = "POST"
		fields["cache_hit_count"] = cacheHitCount
		fields["cache_miss_count"] = cacheMissCount
		fields["lock_acquired_count"] = lockAcquiredCount
		fields["lock_failed_count"] = lockFailedCount
		logger.Logger.InfoWithFiledCtx(ctx, fields, "接口耗时和性能指标")
	}()

	// 环境安全检查 - 只允许在非生产环境使用
	env := os.Getenv("Env")
	if env == "prod" {
		logger.Logger.ErrorfCtx(ctx, "[LoginV2Test] 测试接口不允许在生产环境使用")
		return nil, "", fmt.Errorf("test interface not allowed in production environment")
	}

	// 先检查Redis缓存中的游戏存在性
	gameExistCacheKey := fmt.Sprintf(constants.RedisGameExistKey, req.GameID)
	gameExists, err := redis.Get(ctx, gameExistCacheKey)
	if err == nil {
		// 缓存命中，使用tagged switch处理不同的缓存值
		cacheHitCount++
		switch gameExists {
		case "true":
			// 游戏存在，继续执行
		case "false":
			// 游戏不存在（缓存的否定结果）
			return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
		default:
			// 缓存值异常，清除缓存并查询数据库
			logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 游戏存在性缓存值异常: %s, 清除缓存", gameExists)
			redis.Redis().Del(ctx, gameExistCacheKey)
			// 继续执行数据库查询逻辑
		}
	} else {
		// 缓存未命中，使用分布式锁防止惊群效应
		cacheMissCount++
		// 分布式锁键，防止同一游戏的并发查询
		lockKey := constants.RedisGameExistLockKeyPrefix + req.GameID
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			lockAcquiredCount++
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, gameExistCacheKey); doubleCheckErr == nil {
				switch doubleCheckValue {
				case "true":
					// 继续执行后续逻辑
				case "false":
					return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
				}
			}

			// 执行数据库查询
			_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)

			// 缓存查询结果（包括否定结果）
			cacheValue := "false"
			if err == nil {
				cacheValue = "true"
			}

			// 将结果缓存到Redis，使用常量定义的TTL
			if cacheErr := redis.Set(ctx, gameExistCacheKey, cacheValue, time.Duration(constants.RedisGameExistExpire)*time.Second); cacheErr != nil {
				// 缓存失败不影响主流程，只记录日志
				logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 缓存游戏存在性失败: %v", cacheErr)
			}

			if err != nil {
				return nil, "", err
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, gameExistCacheKey); retryErr == nil {
				switch retryValue {
				case "true":
					// 继续执行后续逻辑
				case "false":
					return nil, "", fmt.Errorf("游戏不存在: gameID=%s", req.GameID)
				}
			}

			// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
			logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 获取游戏存在性锁失败，降级为直接查询, gameID: %s", req.GameID)
			_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)
			if err != nil {
				return nil, "", fmt.Errorf("游戏不存在: gameID=%s, error=%v", req.GameID, err)
			}
		}
	}

	// 参数标准化
	if req.Channel == "" {
		req.Channel = constants.ThinkingdataDefault
	}
	if req.ADFrom == "" {
		req.ADFrom = constants.ThinkingdataDefault
	}

	// 验证测试参数
	if req.TestOpenID == "" {
		return nil, "", fmt.Errorf("test_open_id is required for test interface")
	}

	// 模拟微信session数据
	miniGameSession := &bean.Code2SessionRes{
		OpenID:     req.TestOpenID,
		SessionKey: constants.TestSessionKeyPrefix + req.TestOpenID, // 模拟session_key
		UnionID:    req.TestUnionID,
	}

	// 检查是否为新用户（使用带缓存和分布式锁的优化版本）
	isNewUser, err := l.userService.IsNewUserWithCache(ctx, miniGameSession.OpenID, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[LoginV2Test] 检查是否为新用户失败, game_id: %s, open_id: %s, error: %v",
			req.GameID, miniGameSession.OpenID, err)
		return nil, "", err
	}

	// 停服检查（新用户）
	if isNewUser {
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeMinigame)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginV2Test] 检查新用户注册配置失败, game_id: %s, platform: minigame, error: %v",
				req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 该游戏已关闭新用户注册, game_id: %s, platform: minigame, open_id: %s",
				req.GameID, miniGameSession.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
	}

	// 先尝试从Redis缓存获取用户信息（仅对已存在用户）
	userCacheKey := fmt.Sprintf(constants.RedisUserInfoKey, miniGameSession.OpenID)
	cachedUser, err := redis.Get(ctx, userCacheKey)

	var user *bean.User
	if err == nil && cachedUser != "" {
		// 缓存命中，反序列化用户信息
		user = &bean.User{}
		if jsonErr := json.Unmarshal([]byte(cachedUser), user); jsonErr == nil {
			// 记录缓存命中监控日志
			fields := map[string]interface{}{
				"operation": "user_info_cache_hit",
				"open_id":   miniGameSession.OpenID,
				"cache_key": userCacheKey,
				"data_size": len(cachedUser),
			}
			logger.Logger.InfoWithFiledCtx(ctx, fields, "用户信息缓存命中")
		} else {
			// 缓存数据损坏，清除缓存并查询数据库
			redis.Redis().Del(ctx, userCacheKey)
			user = nil
			// 记录缓存损坏监控日志
			logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 用户信息缓存数据损坏, openID: %s, error: %v", miniGameSession.OpenID, jsonErr)
		}
	}

	if user == nil {
		// 缓存未命中或损坏，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一用户信息的并发查询
		lockKey := fmt.Sprintf("cache:user_info_lock:%s:%s", req.GameID, miniGameSession.OpenID)
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, userCacheKey); doubleCheckErr == nil && doubleCheckValue != "" {
				user = &bean.User{}
				if jsonErr := json.Unmarshal([]byte(doubleCheckValue), user); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					// 缓存数据损坏，清除缓存
					redis.Redis().Del(ctx, userCacheKey)
					user = nil
				}
			}

			if user == nil {
				// 执行数据库查询
				user, err = l.userService.GetUserInfoByOpenIDForTest(ctx, req.GameID, req.Channel, req.ADFrom,
					miniGameSession.OpenID, miniGameSession.UnionID, miniGameSession.SessionKey)
				if err != nil {
					return nil, "", err
				}

				// 缓存策略优化：Write-Through模式 - 统一缓存策略
				cacheManager := utils.NewCacheManager()
				if cacheErr := cacheManager.UpdateUserCache(ctx, miniGameSession.OpenID, user); cacheErr != nil {
					logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 更新用户缓存失败: %v", cacheErr)
				}

				if user.IsRegister {
					// 新注册用户：同时更新IsNewUser缓存状态，确保缓存一致性
					l.updateUserExistsCacheForTest(ctx, miniGameSession.OpenID, req.GameID, false)
					logger.Logger.InfofCtx(ctx, "[LoginV2Test] 新用户注册成功，已更新用户信息和存在性缓存, userID: %s, gameID: %s, openID: %s",
						user.UserID, req.GameID, miniGameSession.OpenID)
				}
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, userCacheKey); retryErr == nil && retryValue != "" {
				user = &bean.User{}
				if jsonErr := json.Unmarshal([]byte(retryValue), user); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					user = nil
				}
			}

			if user == nil {
				// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
				logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 获取用户信息锁失败，降级为直接查询, gameID: %s, openID: %s", req.GameID, miniGameSession.OpenID)
				user, err = l.userService.GetUserInfoByOpenIDForTest(ctx, req.GameID, req.Channel, req.ADFrom,
					miniGameSession.OpenID, miniGameSession.UnionID, miniGameSession.SessionKey)
				if err != nil {
					return nil, "", err
				}
			}
		}
	}

	user.DeviceID = req.DeviceID

	// 重新验证新用户状态，确保缓存一致性
	// 如果用户是通过数据库查询获取的新注册用户，需要重新检查停服配置
	if user.IsRegister && !isNewUser {
		// 发现数据不一致：缓存说是老用户，但数据库显示是新注册
		logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 发现用户状态不一致, 缓存显示老用户但实际是新注册, gameID: %s, openID: %s",
			req.GameID, miniGameSession.OpenID)

		// 重新检查停服配置
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeMinigame)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginV2Test] 重新检查新用户注册配置失败, game_id: %s, platform: minigame, error: %v",
				req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 重新检查发现该游戏已关闭新用户注册, game_id: %s, platform: minigame, open_id: %s",
				req.GameID, miniGameSession.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
	}

	// 优化：使用缓存获取游戏信息

	// 先尝试从Redis缓存获取游戏信息
	gameInfoCacheKey := fmt.Sprintf(constants.RedisGameInfoKey, req.GameID)
	cachedGameInfo, err := redis.Get(ctx, gameInfoCacheKey)

	var gameInfo *model.MGame
	if err == nil && cachedGameInfo != "" {
		// 缓存命中，反序列化游戏信息
		gameInfo = &model.MGame{}
		if jsonErr := json.Unmarshal([]byte(cachedGameInfo), gameInfo); jsonErr == nil {
			// 缓存命中成功
		} else {
			// 缓存数据损坏，清除缓存并查询数据库
			redis.Redis().Del(ctx, gameInfoCacheKey)
			gameInfo = nil
		}
	}

	if gameInfo == nil {
		// 缓存未命中或损坏，使用分布式锁防止惊群效应
		// 分布式锁键，防止同一游戏信息的并发查询
		lockKey := constants.RedisGameInfoLockKeyPrefix + req.GameID
		isLocked := redis.Lock(ctx, lockKey, constants.CacheLockTimeout)

		if isLocked {
			// 获得锁，执行数据库查询和缓存更新
			defer redis.UnLock(ctx, lockKey)

			// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
			if doubleCheckValue, doubleCheckErr := redis.Get(ctx, gameInfoCacheKey); doubleCheckErr == nil && doubleCheckValue != "" {
				gameInfo = &model.MGame{}
				if jsonErr := json.Unmarshal([]byte(doubleCheckValue), gameInfo); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					// 缓存数据损坏，清除缓存
					redis.Redis().Del(ctx, gameInfoCacheKey)
					gameInfo = nil
				}
			}

			if gameInfo == nil {
				// 执行数据库查询
				gameInfo, err = l.userService.GetGameInfo(ctx, req.GameID)
				if err != nil {
					return nil, "", err
				}

				// 将游戏信息缓存到Redis，使用常量定义的TTL
				if gameInfoData, jsonErr := json.Marshal(gameInfo); jsonErr == nil {
					if cacheErr := redis.Set(ctx, gameInfoCacheKey, string(gameInfoData), time.Duration(constants.RedisGameInfoExpire)*time.Second); cacheErr != nil {
						// 缓存失败不影响主流程，只记录日志
						logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 缓存游戏信息失败: %v", cacheErr)
					}
				}
			}
		} else {
			// 未获得锁，等待一小段时间后重试缓存
			time.Sleep(constants.CacheRetryDelay)
			if retryValue, retryErr := redis.Get(ctx, gameInfoCacheKey); retryErr == nil && retryValue != "" {
				gameInfo = &model.MGame{}
				if jsonErr := json.Unmarshal([]byte(retryValue), gameInfo); jsonErr == nil {
					// 继续执行后续逻辑
				} else {
					gameInfo = nil
				}
			}

			if gameInfo == nil {
				// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
				logger.Logger.WarnfCtx(ctx, "[LoginV2Test] 获取游戏信息锁失败，降级为直接查询, gameID: %s", req.GameID)
				gameInfo, err = l.userService.GetGameInfo(ctx, req.GameID)
				if err != nil {
					return nil, "", err
				}
			}
		}
	}

	gameInfo.PlatformAppID = "debug-appid"

	// 优化：如果是新用户注册，异步进行注册相关处理
	if user.IsRegister {
		// 异步进行注册数据上报，避免阻塞主流程
		go func() {
			// 创建带超时的context，避免主请求context取消影响异步任务，同时防止任务无限期运行
			asyncCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := l.dataReportService.ReportUserRegister(asyncCtx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, miniGameSession.OpenID, user.ChannelInfo); err != nil {
				logger.Logger.ErrorfCtx(asyncCtx, "[LoginV2Test] Async ReportUserRegister failed, error: %v", err)
			}
			if err := l.dataReportService.ReportRegister(asyncCtx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, req.OS, user.ChannelInfo); err != nil {
				logger.Logger.ErrorfCtx(asyncCtx, "[LoginV2Test] Async ReportRegister failed, error: %v", err)
			}
		}()
	}

	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	mappedUserID, tokenUserID := l.ApplyUserIDMappingForKof(ctx, user, req.GameID, miniGameSession.UnionID, "LoginV2Test")

	logger.Logger.InfofCtx(ctx, "LoginV2Test user id: %s (token使用ID: %s)", mappedUserID, tokenUserID)

	// 加密用户信息
	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, user.UserInfo)
	if err != nil {
		return nil, "", err
	}

	// 生成JWT token - 使用原始用户ID生成token
	token, err := util.GenerateToken(tokenUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(tokenUserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, miniGameSession.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	// 生成签名
	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	// 优化：异步数据上报（登录事件），避免阻塞主流程
	// 异步进行登录数据上报
	go func() {
		// 创建带超时的context，避免主请求context取消影响异步任务，同时防止任务无限期运行
		asyncCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := l.dataReportService.ReportUserLogin(asyncCtx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, miniGameSession.OpenID, user.RegisterAt, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(asyncCtx, "[LoginV2Test] Async ReportUserLogin failed, error: %v", err)
		}
		if err := l.dataReportService.ReportLogin(asyncCtx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(asyncCtx, "[LoginV2Test] Async ReportLogin failed, error: %v", err)
		}
	}()

	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  user.ChannelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

func (l *UserLogic) LoginSubscribe(ctx context.Context, req *bean.LoginSubscribeReq) (*bean.SubscribeUserInfo, string, error) {
	logger.Logger.InfofCtx(ctx, "LoginSubscribe req: %s", req)

	// 根据order_id查询订单, 获取所属游戏id
	// 之后查询m_config_minigame, 获取is_verify_union_id
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orderInfo, err := orderCtx.Where(order.OrderID.Eq(req.OrderID)).Where(order.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoginSubscribe get order err: %s", err.Error())
		return nil, "", err
	}

	// 查询minigame配置获取is_verify_union_id
	minigameConf := store.QueryDB().AConfigMinigame
	minigameCtx := minigameConf.WithContext(ctx)
	configInfo, err := minigameCtx.Where(minigameConf.GameID.Eq(orderInfo.GameID)).Where(minigameConf.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoginSubscribe get minigame config err: %s", err.Error())
		return nil, "", err
	}

	// 1. 获取配置
	config, err := l.subscribeService.GetSubscribeConfig(ctx)
	if err != nil {
		return nil, "", fmt.Errorf("get subscribe config failed: %w", err)
	}
	if config == nil {
		return nil, "", errors.New("subscribe config not found")
	}

	logger.Logger.InfofCtx(ctx, "LoginSubscribe start, config: %+v, code: %s", config, req.Code)

	// 2. 获取OAuth2 token
	subscribeSession, err := l.subscribeService.GetOauth2AccessToken(ctx, config.AppID, config.AppSercet, req.Code)
	if err != nil {
		return nil, "", fmt.Errorf("get oauth2 token failed: %w", err)
	}
	// 如果返回IsSnapshotUser = 1，则为快照模式
	if subscribeSession.IsSnapshotUser == constants.TrueInt {
		logger.Logger.ErrorfCtx(ctx, "subscribe code is snapshot, openID: %s", subscribeSession.OpenID)
		return nil, "", constants.ErrSubscribeCodeSnapshot
	}

	// 3. 获取UnionID
	var existingUnionID string
	if !configInfo.IsVerifyUnionID {
		// 如果不需要校验union_id，直接设置hasUnionID为true
		userInfo, err := l.userService.GetSubscribeUserInfoByOpenID(ctx, subscribeSession.OpenID, "")
		if err != nil {
			return nil, "", fmt.Errorf("get/create user info failed: %w", err)
		}
		userInfo.HasUnionID = true
		token, err := util.GenerateToken(userInfo.UserID, req.DeviceID, "", "", userInfo.OpenID, "")
		if err != nil {
			return nil, "", fmt.Errorf("generate token failed: %w", err)
		}
		logger.Logger.InfofCtx(ctx, "LoginSubscribe success without union_id verify, userID: %s", userInfo.UserID)
		return userInfo, token, nil
	}

	if subscribeSession.UnionID != "" {
		existingUnionID = subscribeSession.UnionID
		logger.Logger.InfofCtx(ctx, "using unionID from session: %s", existingUnionID)
	} else {
		// 从数据库查询
		existingUnionID, err = l.userService.GetSubscribeUnionIDByOpenID(ctx, subscribeSession.OpenID)
		if err != nil {
			return nil, "", fmt.Errorf("get unionID by openID failed: %w", err)
		}
	}

	// 4. 获取用户信息
	if existingUnionID == "" {
		// 从微信API获取
		wechatUserInfo, err := l.userService.GetWechatUserInfo(ctx, config.AccessToken, subscribeSession.OpenID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "get wechat user info failed: %s", err.Error())
			return nil, "", err
		}
		existingUnionID = wechatUserInfo.UnionID
	}

	// 5. 获取或创建用户
	userInfo, err := l.userService.GetSubscribeUserInfoByOpenID(ctx, subscribeSession.OpenID, existingUnionID)
	if err != nil {
		return nil, "", fmt.Errorf("get/create user info failed: %w", err)
	}

	// 6. 生成token
	token, err := util.GenerateToken(userInfo.UserID, req.DeviceID, "", "", userInfo.OpenID, "")
	if err != nil {
		return nil, "", fmt.Errorf("generate token failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "LoginSubscribe success, userID: %s", userInfo.UserID)
	return userInfo, token, nil
}

// RefreshLogin 刷新登录
// 注意：req.UserID 已经是映射后的旧用户ID，本方法会进行反向映射找到新用户ID用于token生成
func (l *UserLogic) RefreshLogin(ctx context.Context, req *bean.RefreshLogin) (*bean.LoginRes, string, error) {
	logger.Logger.InfofCtx(ctx, "[RefreshLogin] 开始刷新登录, userID: %s (已映射的旧用户ID), gameID: %s, deviceID: %s", req.UserID, req.GameID, req.DeviceID)

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[RefreshLogin] 获取小游戏配置失败, gameID: %s, err: %v", req.GameID, err)
		return nil, "", err
	}

	// 此接口为非加密，检测接口是否被禁止
	if conf.DisableInsecure == constants.TrueInt {
		logger.Logger.WarnfCtx(ctx, "[RefreshLogin] 配置已禁用非加密接口, game_id: %s", req.GameID)
		return nil, "", constants.ErrLoginConfigIsDisable
	}

	// 用户ID映射逻辑 - 临时过渡逻辑
	// req.UserID是旧用户ID，为了sessionKey验证成功，需要找到对应的新用户ID
	// 但后续的加密和token生成仍使用旧用户ID保持一致性
	var sessionUserID = req.UserID // 用于session验证的用户ID，默认使用旧用户ID

	// 先通过旧用户ID获取初始信息，以获取unionID
	tempInfo, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}

	// 如果启用映射功能且是新游戏，尝试找到新用户ID用于session验证
	if req.GameID == config.GlobConfig.UserIDMapping.NewGameID && tempInfo != nil && tempInfo.UnionID != "" && config.GlobConfig.UserIDMapping.Enabled {
		newUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, tempInfo.UnionID, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[RefreshLogin] 查找新用户ID失败, unionID: %s, newGameID: %s, err: %v", tempInfo.UnionID, req.GameID, err)
		} else if newUserID != "" && newUserID != req.UserID {
			// 找到新用户ID，使用它进行session验证
			sessionUserID = newUserID
			logger.Logger.InfofCtx(ctx, "[RefreshLogin] 映射成功，使用新用户ID进行session验证，旧用户ID: %s, 新用户ID: %s, unionID: %s", req.UserID, newUserID, tempInfo.UnionID)
		} else {
			logger.Logger.InfofCtx(ctx, "[RefreshLogin] 未找到对应的新用户ID，使用旧用户ID: %s, unionID: %s", req.UserID, tempInfo.UnionID)
		}
	}

	// 检查session_key 是否有效 - 使用映射后的用户ID
	info, err := l.userService.GetMinigameModel(ctx, sessionUserID)
	if err != nil {
		return nil, "", err
	}
	err = l.httpsWechatService.CheckSessionKey(ctx, info.SessionKey, conf.AccessToken, info.OpenID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[RefreshLogin] SessionKey验证失败, gameid: %s, userid: %s, openid: %s, err: %s", conf.GameID, info.UserID, info.OpenID, err.Error())
		// session验证失败时清除相关缓存，确保一致性
		cacheManager := utils.NewCacheManager()
		cacheManager.InvalidateUserCacheOnSessionFailure(ctx, info.OpenID, "RefreshLogin session验证失败")
		return &bean.LoginRes{}, "", nil
	}

	// 获取secret和game id
	var gameID string
	var secret string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[RefreshLogin] 获取用户信息失败, userID: %s, err: %v", req.UserID, err)
		return nil, "", err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, "", err
	}
	secret = gameInfo.Secret

	// 获取加密前的用户信息 - 始终使用旧用户ID保持一致性
	userInfo, err := l.userService.GetMinigameUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}

	userInfo.DeviceID = req.DeviceID
	encrypt, err := l.secretService.Encrypt(ctx, secret, userInfo)
	if err != nil {
		return nil, "", err
	}

	// 生成token - 继续使用旧用户ID保持用户体验一致性
	token, err := util.GenerateToken(req.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(req.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	// 通过user_id 查找 a_user_share_codes 的表，找到share_code返回
	shareCode, err := l.userService.GetShareCodeByUserID(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}
	channelInfo := &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID, ShareCode: shareCode}

	// 将旧token写入redis 黑名单
	if req.RefreshToken != "" {
		if err = l.userService.SetBlacklistToken(ctx, req.RefreshToken); err != nil {
			return nil, "", err
		}
	}

	logger.Logger.InfofCtx(ctx, "[RefreshLogin] 刷新登录成功, 用户ID: %s, session用户ID: %s, gameID: %s", req.UserID, sessionUserID, req.GameID)
	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  channelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

func (l *UserLogic) RefreshLoginV3(ctx context.Context, req *bean.RefreshLoginV3Req) (*bean.LoginRes, string, error) {
	logger.Logger.InfofCtx(ctx, "[RefreshLoginV3] 开始刷新登录V3, userID: %s (已映射的旧用户ID), gameID: %s, deviceID: %s", req.UserID, req.GameID, req.DeviceID)

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[RefreshLoginV3] 获取小游戏配置失败, gameID: %s, err: %v", req.GameID, err)
		return nil, "", err
	}

	// 用户ID映射逻辑 - 临时过渡逻辑（与RefreshLogin保持一致）
	// req.UserID是旧用户ID，为了sessionKey验证成功，需要找到对应的新用户ID
	// 但后续的加密和token生成仍使用旧用户ID保持一致性
	var sessionUserID = req.UserID // 用于session验证的用户ID，默认使用旧用户ID

	// 先通过旧用户ID获取初始信息，以获取unionID
	tempInfo, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}

	// 如果启用映射功能且是新游戏，尝试找到新用户ID用于session验证
	if req.GameID == config.GlobConfig.UserIDMapping.NewGameID && tempInfo != nil && tempInfo.UnionID != "" && config.GlobConfig.UserIDMapping.Enabled {
		newUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, tempInfo.UnionID, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[RefreshLoginV3] 查找新用户ID失败, unionID: %s, newGameID: %s, err: %v", tempInfo.UnionID, req.GameID, err)
		} else if newUserID != "" && newUserID != req.UserID {
			// 找到新用户ID，使用它进行session验证
			sessionUserID = newUserID
			logger.Logger.InfofCtx(ctx, "[RefreshLoginV3] 映射成功，使用新用户ID进行session验证，旧用户ID: %s, 新用户ID: %s, unionID: %s", req.UserID, newUserID, tempInfo.UnionID)
		} else {
			logger.Logger.InfofCtx(ctx, "[RefreshLoginV3] 未找到对应的新用户ID，使用旧用户ID: %s, unionID: %s", req.UserID, tempInfo.UnionID)
		}
	}

	// 检查session_key 是否有效 - 使用映射后的用户ID
	info, err := l.userService.GetMinigameModel(ctx, sessionUserID)
	if err != nil {
		return nil, "", err
	}

	err = l.httpsWechatService.CheckSessionKey(ctx, info.SessionKey, conf.AccessToken, info.OpenID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[RefreshLoginV3] SessionKey验证失败, gameid: %s, userid: %s, openid: %s, err: %s", conf.GameID, info.UserID, info.OpenID, err.Error())
		// session验证失败时清除相关缓存，确保一致性
		cacheManager := utils.NewCacheManager()
		cacheManager.InvalidateUserCacheOnSessionFailure(ctx, info.OpenID, "RefreshLoginV3 session验证失败")
		return &bean.LoginRes{}, "", nil
	}

	// 解密
	encryptKey, err := l.GetLastUserKeyByOpenID(ctx, req.GameID, info.OpenID, req.Version)
	if err != nil {
		return nil, "", err
	}
	decryptData, err := l.secretService.DecryptData(encryptKey, req.EncryptData, req.IV)
	if err != nil {
		return nil, "", err
	}
	logger.Logger.InfofCtx(ctx, "decryptData: %s", decryptData)
	logger.Logger.InfofCtx(ctx, "encryptKey: %s", encryptKey)
	logger.Logger.InfofCtx(ctx, "req.IV: %s", req.IV)
	logger.Logger.InfofCtx(ctx, "util.EncodeMD5(req.IV+encryptKey): %s", util.EncodeMD5(req.IV+encryptKey))
	if decryptData != util.EncodeMD5(req.IV+encryptKey) {
		return nil, "", fmt.Errorf("decrypt data error, game_id: %s, open_id: %s", req.GameID, info.OpenID)
	}

	// 获取secret和game id
	var gameID string
	var secret string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[RefreshLoginV3] 获取用户信息失败, userID: %s, err: %v", req.UserID, err)
		return nil, "", err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, "", err
	}
	secret = gameInfo.Secret

	// 获取加密前的用户信息 - 始终使用旧用户ID保持一致性
	userInfo, err := l.userService.GetMinigameUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}
	userInfo.DeviceID = req.DeviceID

	encrypt, err := l.secretService.Encrypt(ctx, secret, userInfo)
	if err != nil {
		return nil, "", err
	}

	// 生成token - 继续使用旧用户ID保持用户体验一致性
	token, err := util.GenerateToken(req.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(req.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	// 通过user_id 查找 a_user_share_codes 的表，找到share_code返回
	shareCode, err := l.userService.GetShareCodeByUserID(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}
	channelInfo := &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID, ShareCode: shareCode}

	// 将旧token写入redis 黑名单
	if req.RefreshToken != "" {
		if err = l.userService.SetBlacklistToken(ctx, req.RefreshToken); err != nil {
			return nil, "", err
		}
	}

	logger.Logger.InfofCtx(ctx, "[RefreshLoginV3] 刷新登录V3成功, 用户ID: %s, session用户ID: %s, gameID: %s", req.UserID, sessionUserID, req.GameID)
	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  channelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

func (l *UserLogic) RefreshDouyinLogin(ctx context.Context, req *bean.RefreshDouyinLoginReq) (*bean.LoginRes, string, error) {
	logger.Logger.InfofCtx(ctx, "[RefreshDouyinLogin] 开始刷新抖音登录, userID: %s, deviceID: %s", req.UserID, req.DeviceID)

	var gameID string
	var secret string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		logger.Logger.Error(bizerrors.Wrap(err, "UserLogic RefreshDouyinLogin"))
		return nil, "", err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, "", err
	}
	secret = gameInfo.Secret

	// 获取加密前的用户信息
	userInfo, err := l.userService.GetDouyinUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}
	userInfo.DeviceID = req.DeviceID
	encrypt, err := l.secretService.Encrypt(ctx, secret, userInfo)
	if err != nil {
		return nil, "", err
	}
	token, err := util.GenerateToken(userInfo.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(userInfo.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	if req.RefreshToken != "" {
		if err = l.userService.SetBlacklistToken(ctx, req.RefreshToken); err != nil {
			return nil, "", err
		}
	}

	channelInfo := &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID}

	logger.Logger.InfofCtx(ctx, "[RefreshDouyinLogin] 刷新抖音登录成功, userID: %s", req.UserID)
	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  channelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

func (l *UserLogic) RefreshQQLogin(ctx context.Context, req *bean.RefreshQQLoginReq) (*bean.LoginRes, string, error) {
	var gameID string
	var secret string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		logger.Logger.Error(bizerrors.Wrap(err, "UserLogic RefreshDouyinLogin"))
		return nil, "", err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, "", err
	}
	secret = gameInfo.Secret

	// 获取加密前的用户信息
	userInfo, err := l.qqService.GetQQUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, "", err
	}
	userInfo.DeviceID = req.DeviceID
	encrypt, err := l.secretService.Encrypt(ctx, secret, userInfo)
	if err != nil {
		return nil, "", err
	}
	token, err := util.GenerateToken(userInfo.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(userInfo.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, req.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	if req.RefreshToken != "" {
		if err = l.userService.SetBlacklistToken(ctx, req.RefreshToken); err != nil {
			return nil, "", err
		}
	}

	channelInfo := &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID}
	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  channelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

// UpdateUserInfo 更新用户信息
func (l *UserLogic) UpdateUserInfo(ctx context.Context, req bean.UpdateUserInfoReq) (*bean.LoginRes, error) {
	logger.Logger.Infof("UpdateUserInfo req: %+v", req)
	// 更新小游戏信息，并解密存储到数据库
	minigameInfo, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	wechatUserInfo, err := l.secretService.WechatDataDecrypt(ctx, minigameInfo.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, err
	}
	_, err = l.userService.UpdateMinigameInfo(ctx, req.UserID, wechatUserInfo)
	if err != nil {
		return nil, err
	}

	// 获取用户数据，获取游戏secret
	var gameID string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, err
	}

	// 通过union_id找到新游戏的user_id，获取对应的userinfo
	var actualUserID = req.UserID // 默认使用传入的user_id
	if minigameInfo != nil && minigameInfo.UnionID != "" {
		// 通过union_id查找新游戏中对应的user_id
		newGameUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, minigameInfo.UnionID, config.GlobConfig.UserIDMapping.NewGameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[UpdateUserInfo] 通过union_id查找新游戏user_id失败: %v, UnionID: %s", err, minigameInfo.UnionID)
		} else if newGameUserID != "" {
			actualUserID = newGameUserID
			logger.Logger.InfofCtx(ctx, "[UpdateUserInfo] 找到新游戏user_id: %s, 原user_id: %s, UnionID: %s", newGameUserID, req.UserID, minigameInfo.UnionID)
		}
	}

	userInfo, err := l.userService.GetMinigameUserInfo(ctx, actualUserID)
	if err != nil {
		return nil, err
	}

	// 将userInfo中的user_id设置为传入的req.UserID（旧的user_id）
	userInfo.UserID = req.UserID
	logger.Logger.InfofCtx(ctx, "[UpdateUserInfo] 使用user_id %s获取userinfo，但返回的userinfo.UserID设置为: %s", actualUserID, req.UserID)

	// 打点仍使用原始ID
	var reportUserID = req.UserID
	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, userInfo)
	if err != nil {
		return nil, err
	}
	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, err
	}

	// 完成后打点, 上报 Nickname
	err = l.dataReportService.ReportUserNamePoint(ctx,
		reportUserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.NickName)
	if err != nil {
		logger.Logger.Errorf("UpdateUserInfo ReportUserNamePoint err: %s", err.Error())
		// return nil, err 不影响主逻辑
	}

	// 完成后打点, 上报用户头像
	err = l.dataReportService.ReportUserAvatarPoint(ctx,
		reportUserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.AvatarURL)
	if err != nil {
		logger.Logger.Errorf("UpdateUserInfo ReportUserAvatarPoint err: %s", err.Error())
		// return nil, err 不影响主逻辑
	}
	return &bean.LoginRes{
		Sign:        sign,
		Timestamp:   now,
		EncryptUser: encrypt,
		ChannelInfo: &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID},
	}, nil
}

// UpdateUserInfoV2 更新用户信息v2
func (l *UserLogic) UpdateUserInfoV2(ctx context.Context, req bean.UpdateUserInfoV2Req) (*bean.LoginRes, error) {
	logger.Logger.InfofCtx(ctx, "UpdateUserInfoV2 req: %+v", req)

	// 先获取初始用户信息用于union_id映射
	minigameInfo, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// 通过union_id找到新游戏的user_id，确定实际要操作的用户
	var actualUserID = req.UserID         // 默认使用传入的user_id
	var actualMinigameInfo = minigameInfo // 默认使用原始用户信息
	if minigameInfo != nil && minigameInfo.UnionID != "" {
		// 通过union_id查找新游戏中对应的user_id
		newGameUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, minigameInfo.UnionID, config.GlobConfig.UserIDMapping.NewGameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[UpdateUserInfoV2] 通过union_id查找新游戏user_id失败: %v, UnionID: %s", err, minigameInfo.UnionID)
		} else if newGameUserID != "" {
			actualUserID = newGameUserID
			logger.Logger.InfofCtx(ctx, "[UpdateUserInfoV2] 找到新游戏user_id: %s, 原user_id: %s, UnionID: %s", newGameUserID, req.UserID, minigameInfo.UnionID)

			// 获取新用户ID对应的minigame信息，用于签名验证
			actualMinigameInfo, err = l.userService.GetMinigameModel(ctx, actualUserID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[UpdateUserInfoV2] 获取新用户minigame信息失败: %v, actualUserID: %s", err, actualUserID)
				return nil, err
			}
		}
	}

	// 使用实际用户的SessionKey进行签名验证
	if err := l.secretService.VerifySignature(req.RawData, req.Signature, actualMinigameInfo.SessionKey); err != nil {
		return nil, err
	}

	// 使用实际用户的SessionKey进行数据解密
	wechatUserInfo, err := l.secretService.WechatDataDecrypt(ctx, actualMinigameInfo.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, err
	}

	// 更新实际用户的信息
	if err = l.userService.UpdateMinigameInfoV2(ctx, actualUserID, wechatUserInfo); err != nil {
		return nil, err
	}

	// 获取用户数据，获取游戏secret
	var gameID string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, err
	}

	userInfo, err := l.userService.GetMinigameUserInfo(ctx, actualUserID)
	if err != nil {
		return nil, err
	}

	// 将userInfo中的user_id设置为传入的req.UserID（旧的user_id）
	userInfo.UserID = req.UserID
	logger.Logger.InfofCtx(ctx, "[UpdateUserInfoV2] 使用user_id %s获取userinfo，但返回的userinfo.UserID设置为: %s", actualUserID, req.UserID)

	// 打点仍使用原始ID
	var reportUserID = req.UserID
	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, userInfo)
	if err != nil {
		return nil, err
	}
	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, err
	}

	// 完成后打点, 上报 Nickname
	err = l.dataReportService.ReportUserNamePoint(ctx,
		reportUserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.NickName)
	if err != nil {
		logger.Logger.Errorf("UpdateUserInfo ReportUserNamePoint err: %s", err.Error())
		// return nil, err 不影响主逻辑
	}

	// 完成后打点, 上报用户头像
	err = l.dataReportService.ReportUserAvatarPoint(ctx,
		reportUserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.AvatarURL)
	if err != nil {
		logger.Logger.Errorf("UpdateUserInfoV2 ReportUserAvatarPoint err: %s", err.Error())
		// return nil, err 不影响主逻辑
	}
	return &bean.LoginRes{
		Sign:        sign,
		Timestamp:   now,
		EncryptUser: encrypt,
		ChannelInfo: &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID},
	}, nil
}

// LoginDouyin 抖音登录
func (l *UserLogic) LoginDouyin(ctx context.Context, req *bean.LoginDouyinReq) (*bean.LoginRes, string, error) {
	_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}

	// 参数标准化
	if req.Channel == "" {
		req.Channel = constants.ThinkingdataDefault
	}
	if req.ADFrom == "" {
		req.ADFrom = constants.ThinkingdataDefault
	}

	conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	douyin, err := l.douyinService.LoginDouyin(ctx, req.Code, &bean.DouyinParam{
		AppID:     conf.AppID,
		AppSecret: conf.AppSecret,
	})
	if err != nil {
		return nil, "", err
	}

	// 在创建用户前，先检查是否为新用户，以及是否允许新用户注册
	isNewUser, err := l.douyinService.IsNewUser(ctx, douyin.OpenID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[LoginDouyin] 检查是否为新用户失败, game_id: %s, douyin_open_id: %s, error: %v", req.GameID, douyin.OpenID, err)
		return nil, "", err
	}

	if isNewUser {
		// 如果是新用户，先检查是否允许注册
		logger.Logger.InfofCtx(ctx, "[LoginDouyin] 检测到新用户，开始检查停服配置, game_id: %s, platform: douyin_minigame, douyin_open_id: %s", req.GameID, douyin.OpenID)
		allowRegistration, err := l.stopServiceConfigService.CheckNewUserRegistrationEnabled(ctx, req.GameID, constants.PlatformTypeDouyin)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginDouyin] 检查新用户注册配置失败, game_id: %s, platform: douyin_minigame, error: %v", req.GameID, err)
			return nil, "", err
		}
		if !allowRegistration {
			logger.Logger.WarnfCtx(ctx, "[LoginDouyin] 该游戏已关闭新用户注册, game_id: %s, platform: douyin_minigame, douyin_open_id: %s", req.GameID, douyin.OpenID)
			return nil, "", constants.ErrGameRegistrationClosed
		}
		logger.Logger.InfofCtx(ctx, "[LoginDouyin] 停服配置检查通过，允许新用户注册, game_id: %s, platform: douyin_minigame, douyin_open_id: %s", req.GameID, douyin.OpenID)
	}

	user, err := l.douyinService.GetUserInfo(ctx, req.GameID, req.Code,
		&bean.DouyinUser{OpenID: douyin.OpenID, UnionID: douyin.UnionID, SessionKey: douyin.SessionKey},
		&bean.ChannelInfo{OpenID: douyin.OpenID, Channel: req.Channel, ADFrom: req.ADFrom},
	)
	if err != nil {
		return nil, "", err
	}
	user.DeviceID = req.DeviceID

	// 获取游戏secret
	gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, user.UserInfo)
	if err != nil {
		return nil, "", err
	}

	// 下发jwt
	logger.Logger.Infof("登录抖音用户信息, 用户ID: %s, 设备ID: %s, 平台应用ID: %s, 游戏ID: %s, 抖音OpenID: %s, Os: %s",
		user.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, douyin.OpenID, req.OS, req.Channel)
	token, err := util.GenerateToken(user.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, douyin.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(user.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, douyin.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	// 打点登录或注册
	if user.IsRegister {
		if err := l.dataReportService.ReportUserRegister(ctx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, douyin.OpenID, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(ctx, "LoginDouyin ReportUserRegister err: %s", err.Error())
		}
		if err := l.dataReportService.ReportRegister(ctx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, req.OS, user.ChannelInfo); err != nil {
			logger.Logger.ErrorfCtx(ctx, "LoginDouyin ReportRegister err: %s", err.Error())
		}
	}

	if err := l.dataReportService.ReportUserLogin(ctx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, douyin.OpenID, user.RegisterAt, nil); err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoginDouyin ReportUserLogin err: %s", err.Error())
	}
	if err := l.dataReportService.ReportLogin(ctx, user.UserID, req.DeviceID, req.GameID, gameInfo.PlatformAppID, nil); err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoginDouyin ReportLogin err: %s", err.Error())
	}

	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  user.ChannelInfo,
		RefreshToken: refreshToken,
	}, token, nil
}

// LoginQQ 登录QQ
func (l *UserLogic) LoginQQ(ctx context.Context, req *bean.LoginQQReq) (*bean.LoginRes, string, error) {
	_, err := l.minigameService.IsGameIDExist(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	conf, err := l.qqService.GetQQConf(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	loginResp, err := l.qqService.LoginQQ(ctx, req.Code, conf.AccessToken, conf.AppID, conf.AppSecret)
	if err != nil {
		return nil, "", err
	}
	user, err := l.qqService.GetUserInfo(ctx, req.GameID, req.Code, loginResp, &bean.ChannelInfo{OpenID: loginResp.OpenID, Channel: req.Channel, ADFrom: req.ADFrom})
	if err != nil {
		return nil, "", err
	}
	user.DeviceID = req.DeviceID
	// 获取游戏secret
	gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	if err != nil {
		return nil, "", err
	}
	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, user.UserInfo)
	if err != nil {
		return nil, "", err
	}

	token, err := util.GenerateToken(user.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, loginResp.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}
	refreshToken, err := util.GenerateRefreshToken(user.UserID, req.DeviceID, gameInfo.PlatformAppID, gameInfo.GameID, loginResp.OpenID, req.OS)
	if err != nil {
		return nil, "", err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, "", err
	}

	return &bean.LoginRes{
		Sign:         sign,
		Timestamp:    now,
		EncryptUser:  encrypt,
		ChannelInfo:  &bean.ChannelInfo{Channel: user.Channel, ADFrom: user.ADFrom, OpenID: loginResp.OpenID},
		RefreshToken: refreshToken,
	}, token, nil
}

// UpdateDouyinUserInfo 更新小游戏信息，并解密存储到数据库
func (l *UserLogic) UpdateDouyinUserInfo(ctx context.Context, req *bean.UpdateDouyinUserInfoReq) (*bean.LoginRes, error) {
	logger.Logger.InfofCtx(ctx, "UpdateDouyinUserInfo req: %+v", req)
	info, err := l.userService.GetUserDouyinModel(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	// 校验signature
	if err := l.secretService.VerifySignature(req.RawData, req.Signature, info.SessionKey); err != nil {
		return nil, err
	}

	douyinUserInfo, err := l.secretService.DouyinDecrypt(info.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, err
	}
	if err := l.userService.UpdateDouyinInfo(ctx, req.UserID, douyinUserInfo); err != nil {
		return nil, err
	}

	// 获取用户数据，获取游戏secret
	var gameID string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, err
	}
	userInfo, err := l.userService.GetDouyinUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, userInfo)
	if err != nil {
		return nil, err
	}
	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, err
	}

	// 完成后打点, 上报 Nickname
	err = l.dataReportService.ReportUserNamePoint(ctx,
		req.UserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.NickName)
	if err != nil {
		logger.Logger.Errorf("UpdateDouyinUserInfo ReportUserNamePoint err: %s", err.Error())
	}

	// 完成后打点, 上报用户头像
	err = l.dataReportService.ReportUserAvatarPoint(ctx,
		req.UserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.AvatarURL)
	if err != nil {
		logger.Logger.Errorf("UpdateDouyinUserInfo ReportUserAvatarPoint err: %s", err.Error())
	}
	return &bean.LoginRes{
		Sign:        sign,
		Timestamp:   now,
		EncryptUser: encrypt,
		ChannelInfo: &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID},
	}, nil
}

// UpdateQQUserInfo
func (l *UserLogic) UpdateQQUserInfo(ctx context.Context, req *bean.UpdateQQUserInfoReq) (*bean.LoginRes, error) {
	logger.Logger.InfofCtx(ctx, "UpdateQQUserInfo req: %+v", req)
	info, err := l.qqService.GetUserQQModel(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	req.RawData = l.secretService.SortRawData(req.RawData)
	// 校验signature
	if err := l.secretService.VerifySignature(req.RawData, req.Signature, info.SessionKey); err != nil {
		return nil, err
	}

	qqUserInfo, err := l.secretService.WechatDataDecrypt(ctx, info.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, err
	}

	if err := l.qqService.UpdateQQInfo(ctx, req.UserID, qqUserInfo); err != nil {
		return nil, err
	}

	// 获取用户数据，获取游戏secret
	var gameID string
	userModel, err := l.userService.GetUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	gameID = userModel.GameID
	gameInfo, err := l.userService.GetGameInfo(ctx, gameID)
	if err != nil {
		return nil, err
	}
	userInfo, err := l.qqService.GetQQUserInfo(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	if userInfo == nil {
		return nil, constants.ErrUserIDNotFound
	}

	encrypt, err := l.secretService.Encrypt(ctx, gameInfo.Secret, userInfo)
	if err != nil {
		return nil, err
	}

	now := time.Now().UnixMilli()
	sign, err := l.secretService.Sign(ctx, gameInfo.Secret, now, encrypt)
	if err != nil {
		return nil, err
	}

	// 完成后打点, 上报 NickName
	err = l.dataReportService.ReportUserNamePoint(ctx, req.UserID, req.DeviceID, gameInfo.GameID, gameInfo.PlatformAppID, userInfo.NickName)
	if err != nil {
		logger.Logger.Errorf("UpdateQQUserInfo ReportUserNamePoint err: %s", err.Error())
	}

	return &bean.LoginRes{
		Sign:        sign,
		Timestamp:   now,
		EncryptUser: encrypt,
		ChannelInfo: &bean.ChannelInfo{Channel: userModel.Channel, ADFrom: userModel.AdFrom, OpenID: req.OpenID},
	}, nil
}

// GetLastUserKey 获取最后一个用户key
func (l *UserLogic) GetLastUserKey(ctx context.Context, req *bean.GetLastUserKeyReq) (*bean.GetLastUserKeyResp, error) {
	// 获取配置和用户信息
	conf, user, err := l.getConfigAndUser(ctx, req)
	if err != nil {
		return nil, err
	}

	// 获取签名和加密密钥
	wechatUserKeys, err := l.getUserKeys(ctx, conf.AccessToken, user.SessionKey, user.OpenID)
	if err != nil {
		return nil, err
	}

	// 讲userKeys的最后一次结果放入*bean.GetLastUserKeyResp
	data, err := l.convertAndValidateData(wechatUserKeys)
	if err != nil {
		return nil, err
	}

	return &bean.GetLastUserKeyResp{Data: data}, nil
}

func (l *UserLogic) getConfigAndUser(ctx context.Context, req *bean.GetLastUserKeyReq) (*model.AConfigMinigame, *model.AUserMinigame, error) {
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get minigame config: %w", err)
	}

	user, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get user model: %w", err)
	}

	return conf, user, nil
}

func (l *UserLogic) getUserKeys(ctx context.Context, token, sessionKey, openID string) (*bean.WechatEncryptKey, error) {
	// 记录关键参数用于调试
	logger.Logger.InfofCtx(ctx, "[getUserKeys] 开始获取用户加密密钥, openID: %s, token长度: %d, sessionKey长度: %d",
		openID, len(token), len(sessionKey))

	// 根据微信小游戏官方文档生成签名：signature = hmac_sha256(session_key, "")
	signature, err := l.wechatPayService.Signature(sessionKey, []byte(""))
	if err != nil {
		return nil, fmt.Errorf("getUserKeys failed to generate signature: %w", err)
	}

	wechatUserKeys, err := l.minigameService.GetUserEncryptKey(ctx, token, openID, signature, constants.SecretSha256Type)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[getUserKeys] 微信API调用失败, openID: %s, err: %v", openID, err)
		return nil, fmt.Errorf("getUserKeys failed to get user encrypt key: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[getUserKeys] 成功获取用户加密密钥, openID: %s, key数量: %d",
		openID, len(wechatUserKeys.KeyInfoList))

	return wechatUserKeys, nil
}

func (l *UserLogic) convertAndValidateData(wechatUserKeys *bean.WechatEncryptKey) ([]*bean.LastUserKeyData, error) {
	data := make([]*bean.LastUserKeyData, 0, len(wechatUserKeys.KeyInfoList))
	for _, v := range wechatUserKeys.KeyInfoList {
		data = append(data, &bean.LastUserKeyData{
			EncryptKey: v.EncryptKey,
			CreateTime: v.CreateTime,
			ExpireIn:   v.ExpireIn,
			IV:         v.IV,
			Version:    v.Version,
		})
	}

	if len(data) == 0 {
		return nil, errors.New("convertAndValidateData wechatUserKeys is empty, check wechatUserKeys")
	}

	return data, nil
}

func (l *UserLogic) GetLastUserKeyByOpenID(ctx context.Context, gameID, openID string, version int32) (string, error) {
	// 获取配置和用户信息
	conf, err := l.userService.GetMinigameModelByOpenID(ctx, openID)
	if err != nil {
		return "", err
	}

	accessToken, err := l.userService.GetAccessTokenByOpenID(ctx, openID)
	if err != nil {
		return "", err
	}

	// 获取签名和加密密钥
	wechatUserKeys, err := l.getUserKeys(ctx, accessToken, conf.SessionKey, conf.OpenID)
	if err != nil {
		return "", err
	}

	// 找到version值最大的key
	if len(wechatUserKeys.KeyInfoList) == 0 {
		return "", fmt.Errorf("no key info found")
	}

	// 查找指定版本或最新版本的密钥
	key := findKeyByVersion(wechatUserKeys.KeyInfoList, version)
	if key == nil {
		return "", fmt.Errorf("failed to find valid encryption key")
	}

	return key.EncryptKey, nil
}

func findKeyByVersion(keys []*bean.WechatKeyInfoList, targetVersion int32) *bean.WechatKeyInfoList {
	if len(keys) == 0 {
		return nil
	}

	// 初始化为第一个元素
	maxVersionKey := keys[0]

	// 遍历找出最大版本的key
	for _, key := range keys {
		// 如果指定了版本号且找到匹配的，直接返回
		if targetVersion > 0 && key.Version == targetVersion {
			return key
		}
		// 更新最大版本的key
		if key.Version > maxVersionKey.Version {
			maxVersionKey = key
		}
	}

	// 如果是查找最大版本(targetVersion = 0)或未找到指定版本，返回最大版本的key
	if targetVersion == 0 {
		return maxVersionKey
	}

	// 如果指定版本未找到，返回nil
	return nil
}

// updateUserExistsCacheForTest 专门为LoginV2Test接口更新用户存在性缓存
// 这是一个专用方法，确保不影响其他登录接口的行为
func (l *UserLogic) updateUserExistsCacheForTest(ctx context.Context, openID, gameID string, isNewUser bool) {
	// 参数验证
	if openID == "" || gameID == "" {
		logger.Logger.WarnfCtx(ctx, "[updateUserExistsCacheForTest] 参数无效，跳过缓存更新, openID: %s, gameID: %s", openID, gameID)
		return
	}

	// 异步更新缓存，确保不阻塞主业务流程
	go func() {
		// 创建带超时的context，避免主请求context取消影响异步任务，同时防止任务无限期运行
		asyncCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 使用常量定义的缓存键格式
		cacheKey := fmt.Sprintf(constants.RedisUserExistsKey, gameID, openID)
		cacheValue := "1" // 老用户
		if isNewUser {
			cacheValue = "0" // 新用户
		}

		// 写入缓存，使用常量定义的TTL
		if err := redis.Set(asyncCtx, cacheKey, cacheValue, time.Duration(constants.RedisUserExistsExpire)*time.Second); err != nil {
			logger.Logger.WarnfCtx(asyncCtx, "[updateUserExistsCacheForTest] 缓存更新失败, key: %s, error: %v", cacheKey, err)
		} else {
			logger.Logger.InfofCtx(asyncCtx, "[updateUserExistsCacheForTest] 缓存更新成功, key: %s, value: %s", cacheKey, cacheValue)
		}
	}()
}

// ApplyUserIDMappingForKof 为kof游戏应用用户ID映射逻辑（临时过渡逻辑）
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
// 只针对kof游戏且存在hlxq的union_id的老用户生效
// 当系统明确检测到mappedUserID存在用户ID映射关系时，Channel字段使用mappedUserID对应的m_user记录中的Channel值和createAt时间戳数据
func (l *UserLogic) ApplyUserIDMappingForKof(ctx context.Context, user *bean.User, gameID, unionID, logPrefix string) (mappedUserID string, originalUserID string) {
	// 保存原始用户ID，用于token生成和日志记录
	originalUserID = user.UserID
	mappedUserID = user.UserID // 默认映射后ID等于原始ID

	// 使用新统一方法减少重复查询（gameID == "mygame" 且 unionID 非空才尝试）
	if gameID == config.GlobConfig.UserIDMapping.NewGameID && unionID != "" {
		status, err := l.userService.GetUserNewStatusAndMappedIDCached(ctx, unionID, gameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[%s] 获取用户映射状态失败, UnionID: %s, 错误: %v", logPrefix, unionID, err)
			return mappedUserID, originalUserID // 降级：保持原始ID
		}
		if status.IsNewUser {
			logger.Logger.InfofCtx(ctx, "[%s] 检测到 mygame 新用户，跳过用户ID映射, UnionID: %s, 原UserID: %s", logPrefix, unionID, user.UserID)
			return mappedUserID, originalUserID
		}
		if status.MappedUserID != "" && status.MappedUserID != user.UserID { // 仅当有不同映射ID
			// 查询映射用户的Channel和createAt信息
			mappedChannelInfo, err := l.userService.GetMappedUserChannelInfo(ctx, status.MappedUserID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[%s] 获取映射用户渠道信息失败, 映射UserID: %s, 错误: %v, 将使用原始用户信息",
					logPrefix, status.MappedUserID, err)
				// 降级：仍然使用映射的UserID，但保持原始的Channel信息
				user.UserID = status.MappedUserID
				mappedUserID = status.MappedUserID
			} else {
				// 成功获取映射用户信息，更新UserID、Channel和createAt
				logger.Logger.InfofCtx(ctx, "[%s] 用户ID映射成功，使用映射用户的Channel信息, 原UserID: %s, 映射UserID: %s, 原Channel: %s->%s, 原ADFrom: %s->%s, 原RegisterAt: %d->%d, UnionID: %s",
					logPrefix, user.UserID, status.MappedUserID,
					user.Channel, mappedChannelInfo.Channel,
					user.ADFrom, mappedChannelInfo.ADFrom,
					user.RegisterAt, mappedChannelInfo.CreatedAt,
					unionID)

				// 更新用户信息：使用映射用户的UserID、Channel、ADFrom和createAt
				user.UserID = status.MappedUserID
				if user.ChannelInfo != nil {
					user.Channel = mappedChannelInfo.Channel
					user.ADFrom = mappedChannelInfo.ADFrom
				}
				if user.UserInfo != nil {
					user.RegisterAt = mappedChannelInfo.CreatedAt
				}
				mappedUserID = status.MappedUserID
			}
		}
	}

	return mappedUserID, originalUserID
}
