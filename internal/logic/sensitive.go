package logic

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"unicode/utf8"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/cron"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_sensitiveOnce  sync.Once
	_sensitiveLogic *SensitiveLogic
)

type SensitiveLogic struct {
	sensitiveService    *service.SensitiveService
	minigameService     *service.MinigameService
	userService         *service.UserService
	trieService         *service.TrieService
	douyinService       *service.DouyinService
	tencentCloudService *service.TencentCloudService
}

func SingletonSensitiveLogic() *SensitiveLogic {
	_sensitiveOnce.Do(func() {
		_sensitiveLogic = &SensitiveLogic{
			sensitiveService:    service.SingletonSensitiveService(),
			minigameService:     service.SingletonMinigameService(),
			userService:         service.SingletonUserService(),
			trieService:         service.SingletonTrieService(),
			douyinService:       service.SingletonDouyinService(),
			tencentCloudService: service.SingletonTencentCloudService(),
		}
	})
	return _sensitiveLogic
}

// VerifySensitiveMessage 敏感词检测
func (l *SensitiveLogic) VerifySensitiveMessage(ctx context.Context, req *bean.VerifySensitiveMessageReq) (*bean.VerifySensitiveMessageRes, error) {
	logger.Logger.Infof("SensitiveLogic VerifySensitiveMessage req: %+v", req)
	if len(req.Msg) > constants.SensitiveWordMaxLength {
		return nil, fmt.Errorf("SensitiveService VerifySensitiveMessage msg length > 2500")
	}

	resp := &bean.VerifySensitiveMessageRes{}
	var err error
	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		resp, err = l.wechatSensitiveMessage(ctx, req.GameID, req.UserID, req.Msg, req.PlatformType)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic wechatSensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic wechat FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	case constants.PlatformTypeDouyin:
		resp, err = l.douyinSensitiveMessage(ctx, req.GameID, req.Msg)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic douyinSensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic douyin FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	}

	wordMap := cron.GetSensitiveWordLevelByGameID(req.GameID)
	ignoreCase := cron.GetSensitiveWordConfigByGameID(req.GameID)
	for word := range wordMap {
		l.trieService.AddWord(word)
	}

	// 利用反查询把词汇和等级找出
	words := l.trieService.CheckByCase(req.Msg, ignoreCase)

	var detail []*bean.SensitiveMessagePlatformDetail
	for _, word := range words {
		detail = append(detail, &bean.SensitiveMessagePlatformDetail{
			Keyword: word,
			Level:   wordMap[word],
		})
	}
	resp.PlatformDetail = detail

	if resp.ReplacedContent != "" {
		resp.ReplacedContent = l.trieService.CheckAndReplaceByCase(resp.ReplacedContent, ignoreCase)
		return resp, nil
	}
	resp.ReplacedContent = l.trieService.CheckAndReplaceByCase(req.Msg, ignoreCase)
	return resp, nil
}

// MapUserIDForKofGame 为kof游戏映射用户ID（临时过渡逻辑）
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
func (l *SensitiveLogic) MapUserIDForKofGame(ctx context.Context, userID, gameID, logPrefix string) (mappedUserID string, mappedUser *model.AUserMinigame, err error) {
	mappedUserID = userID
	user, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		return "", nil, err
	}

	// 配置驱动：仅当开启映射开关且当前游戏ID等于新游戏ID且存在UnionID时执行映射逻辑
	mapConf := config.GlobConfig.UserIDMapping
	if mapConf.Enabled && gameID == mapConf.NewGameID && user.UnionID != "" {
		// 检查用户在 hlxq 和 kof 游戏中的账户数量，判断是否为新用户（批量一次查询代替两次调用）
		userIDsMap, batchErr := l.userService.GetUserIDsByUnionIDSafe(ctx, user.UnionID, []string{config.GlobConfig.UserIDMapping.OldGameID, config.GlobConfig.UserIDMapping.NewGameID})
		hlxqUserID := userIDsMap[config.GlobConfig.UserIDMapping.OldGameID]
		kofUserID := userIDsMap[config.GlobConfig.UserIDMapping.NewGameID]
		// 为保持原日志语义，构造与之前相似的错误变量（批量安全查询失败时忽略）
		var hlxqErr error
		var kofErr error
		_ = batchErr // 批量安全查询失败等价于两个均空，不影响主流程

		// 统计找到的有效账户数量
		accountCount := 0
		if hlxqErr == nil && hlxqUserID != "" {
			accountCount++
		}
		if kofErr == nil && kofUserID != "" {
			accountCount++
		}

		logger.Logger.InfofCtx(ctx, "[%s] 用户账户检查, UnionID: %s, hlxq用户: %s, kof用户: %s, 账户数量: %d",
			logPrefix, user.UnionID, hlxqUserID, kofUserID, accountCount)

		// 如果只有一个账户，说明是新用户，无需映射
		if accountCount <= 1 {
			logger.Logger.InfofCtx(ctx, "[%s] 检测到新用户，无需进行用户ID映射, 原UserID: %s, UnionID: %s, GameID: %s",
				logPrefix, userID, user.UnionID, gameID)
		} else {
			// 有多个账户，说明是老用户，需要进行映射
			// 使用 kof 游戏中的用户ID作为映射结果
			if kofUserID != "" {
				logger.Logger.InfofCtx(ctx, "[%s] 用户ID映射成功, 原UserID: %s, 映射UserID: %s, UnionID: %s, GameID: %s",
					logPrefix, userID, kofUserID, user.UnionID, gameID)

				// 使用映射后的UserID
				mappedUserID = kofUserID

				// 重新获取映射后用户的信息
				if mappedInfo, mappedErr := l.userService.GetMinigameModel(ctx, mappedUserID); mappedErr == nil {
					user = mappedInfo
				} else {
					logger.Logger.WarnfCtx(ctx, "[%s] 获取映射用户信息失败, 映射UserID: %s, 错误: %v", logPrefix, mappedUserID, mappedErr)
				}
			} else {
				logger.Logger.InfofCtx(ctx, "[%s] 用户ID映射未找到对应用户, 原UserID: %s, UnionID: %s, GameID: %s",
					logPrefix, userID, user.UnionID, gameID)
			}
		}
	}

	return mappedUserID, user, nil
}

func (l *SensitiveLogic) wechatSensitiveMessage(ctx context.Context, gameID, userID, msg, platformType string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}
	conf, err := l.minigameService.GetMinigameConfig(ctx, gameID)
	if err != nil {
		return nil, err
	}

	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	mappedUserID, user, err := l.MapUserIDForKofGame(ctx, userID, gameID, "敏感词检查")
	if err != nil {
		return nil, err
	}

	// 使用映射后的用户信息进行敏感词检查
	logger.Logger.InfofCtx(ctx, "[敏感词检查] 使用UserID进行敏感词检查: %s, OpenID: %s", mappedUserID, user.OpenID)
	result, err := l.sensitiveService.VerifySensitiveMessage(ctx, user.OpenID, conf.AccessToken, msg)
	if err != nil {
		return nil, err
	}
	resp.TraceID = result.TraceID
	resp.ErrCode = result.ErrCode
	resp.ErrMsg = result.ErrMsg
	resp.Source = constants.PlatformTypeMinigame
	resp.Result = result.Result
	resp.Detail = result.Detail
	resp.ReplacedContent = result.Result.ReplacedContent
	return resp, nil
}

func (l *SensitiveLogic) douyinSensitiveMessage(ctx context.Context, gameID, msg string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}
	conf, err := l.douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		return nil, err
	}
	result, err := l.sensitiveService.VerifyDouyinSensitiveMessage(ctx, conf.AccessToken, msg)
	if err != nil {
		return nil, err
	}
	if len(result.Data) == 0 {
		return nil, fmt.Errorf("VerifyDouyinSensitiveMessage data len is zero, gameID: %s", gameID)
	}
	r := result.Data[0]
	resp.TraceID = result.LogID
	resp.ErrCode = r.Code
	resp.ErrMsg = r.Msg
	resp.Source = constants.PlatformTypeDouyin
	resp.Detail = r.Predicts
	resp.Result = l.handleDouyinSensitiveResult(ctx, msg, r.Predicts)
	if resp.Result.ReplacedContent != "" {
		resp.ReplacedContent = resp.Result.ReplacedContent
	}
	return resp, nil
}

func (l *SensitiveLogic) handleDouyinSensitiveResult(ctx context.Context, msg string, predicts []bean.DouyinSecurityPredict) bean.WechatSecurityCheckResult {
	// 遍历predicts，找到第一个hit为true的，然后返回
	// 检测结果-置信度-概率，值为 0 或者 1，当值为 1 时表示检测的文本包含违法违规内容
	for _, predict := range predicts {
		if predict.Prob == constants.SensitiveProbRisk {
			return bean.WechatSecurityCheckResult{
				Suggest:         constants.SensitiveSuggestRisk,
				Label:           constants.SensitiveLabelRisk,
				ReplacedContent: l.replaceSensitiveWord(msg),
			}
		}
	}
	return bean.WechatSecurityCheckResult{
		Suggest:         constants.SensitiveSuggestPass,
		Label:           constants.SensitiveLabelPass,
		ReplacedContent: msg,
	}
}

func (l *SensitiveLogic) replaceSensitiveWord(msg string) string {
	// 如果发现prob为1，则将msg中的蚊子全部替换为*
	charCount := utf8.RuneCountInString(msg)
	return strings.Repeat("*", charCount)
}
