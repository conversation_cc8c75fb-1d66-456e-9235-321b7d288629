package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

var (
	_shareOnce  sync.Once
	_shareLogic *ShareLogic
)

type ShareLogic struct {
	shareService    *service.ShareService
	minigameService *service.MinigameService
	userService     *service.UserService
	secretService   *service.SecretService
	sensitiveLogic  *SensitiveLogic // 用于调用用户ID映射逻辑
	userLogic       *UserLogic      // 用于调用用户ID映射逻辑
}

func SingletonShareLogic() *ShareLogic {
	_shareOnce.Do(func() {
		_shareLogic = &ShareLogic{
			shareService:    service.SingletonShareService(),
			minigameService: service.SingletonMinigameService(),
			userService:     service.SingletonUserService(),
			secretService:   service.SingletonSecretService(),
			sensitiveLogic:  SingletonSensitiveLogic(), // 注入敏感词逻辑，用于用户ID映射
			userLogic:       SingletonUserLogic(),      // 注入用户逻辑，用于用户ID映射
		}
	})
	return _shareLogic
}

// GetShare 获取分享
func (l *ShareLogic) GetShare(ctx context.Context, req *bean.GetShareReq) (*bean.GetShareResp, error) {
	if req.PlatformType == "" {
		req.PlatformType = constants.PlatformTypeMinigame
	}

	shareKey := fmt.Sprintf(constants.BizConfigShare, req.GameID, req.PlatformType)

	var resp *bean.GetShareResp
	shareCache, err := redis.Get(ctx, shareKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	} else if !errors.Is(err, redis.Nil) {
		err := json.Unmarshal([]byte(shareCache), &resp)
		if err != nil {
			return nil, err
		}
		return resp, nil
	}

	shares, err := l.shareService.GetShare(ctx, req)
	if err != nil {
		return nil, err
	}
	if shares == nil || len(shares.ShareInfos) == 0 {
		return shares, nil
	}
	// 使用redis存储game id的值
	sharesStr, err := json.Marshal(shares)
	if err != nil {
		return nil, err
	}
	err = redis.Set(ctx, shareKey, sharesStr, constants.OneDay)
	if err != nil {
		return nil, err
	}
	return shares, nil
}

// CreateUserActivityID 创建私密分享
func (l *ShareLogic) CreateUserActivityID(ctx context.Context, req *bean.CreateUserActivityIDReq) (*bean.CreateUserActivityIDResp, error) {
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	activity, err := l.shareService.FetchCreateActivityID(ctx, conf.AccessToken, req.OpenID)
	if err != nil {
		return nil, err
	}

	if activity.ActivityID == "" {
		return nil, constants.ErrActivityIDIsNil
	}

	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	// 获取用户信息以获取UnionID
	user, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// 构造用户bean，用于映射逻辑
	userBean := &bean.User{
		UserInfo: &bean.UserInfo{
			UserID: req.UserID,
		},
	}

	// 应用用户ID映射逻辑
	mappedUserID, _ := l.userLogic.ApplyUserIDMappingForKof(ctx, userBean, req.GameID, user.UnionID, "私密分享创建")

	logger.Logger.InfofCtx(ctx, "[私密分享创建] 使用UserID生成ActivityKey: %s", mappedUserID)
	activityKey := util.EncodeMD5Salt(activity.ActivityID+mappedUserID, constants.Md5Salt)
	logger.Logger.InfofCtx(ctx, "CreateUserActivityID activityKey: %s", activityKey)
	logger.Logger.InfofCtx(ctx, "CreateUserActivityID activity.ActivityID: %s", activity.ActivityID)
	logger.Logger.InfofCtx(ctx, "CreateUserActivityID mappedUserID: %s", mappedUserID)
	logger.Logger.InfofCtx(ctx, "CreateUserActivityID constants.Md5Salt: %s", constants.Md5Salt)
	return &bean.CreateUserActivityIDResp{
		ActivityID:     activity.ActivityID,
		ExpirationTime: activity.ExpirationTime,
		ActivityKey:    activityKey,
	}, nil
}

// DecryptUserPrivateShare 解密私密分享
func (l *ShareLogic) DecryptUserPrivateShare(ctx context.Context, req *bean.DecryptUserPrivateShareReq) (*bean.DecryptUserPrivateShareResp, error) {
	logger.Logger.InfofCtx(ctx, "DecryptUserPrivateShare req: %+v", req)

	if req.ShareUserID == "" {
		logger.Logger.WarnfCtx(ctx, "DecryptUserPrivateShare req.ShareUserID is nil, game_id: %s, user_id: %s", req.GameID, req.UserID)
		return nil, constants.ErrShareUserIDIsNil
	}

	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	mappedUserID, user, err := l.sensitiveLogic.MapUserIDForKofGame(ctx, req.UserID, req.GameID, "私密分享解密")
	if err != nil {
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[私密分享解密] 使用UserID进行解密: %s, OpenID: %s", mappedUserID, user.OpenID)
	activityID, err := l.secretService.WechatDataDecryptStr(user.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "DecryptUserPrivateShare activityID: %s", activityID)
	logger.Logger.InfofCtx(ctx, "DecryptUserPrivateShare req.ActivityKey: %s", req.ActivityKey)
	logger.Logger.InfofCtx(ctx, "DecryptUserPrivateShare req.ShareUserID: %s", req.ShareUserID)
	logger.Logger.InfofCtx(ctx, "DecryptUserPrivateShare mappedUserID: %s", mappedUserID)
	logger.Logger.InfofCtx(ctx, "DecryptUserPrivateShare constants.Md5Salt: %s", constants.Md5Salt)
	// 防止篡改, 解密后校验
	if util.EncodeMD5Salt(activityID+req.ShareUserID, constants.Md5Salt) != req.ActivityKey {
		return nil, constants.ErrActivityIDInvalid
	}
	return &bean.DecryptUserPrivateShareResp{ActivityID: activityID}, nil
}
