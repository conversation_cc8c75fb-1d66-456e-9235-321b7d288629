# 用户ID映射功能实现说明

## 概述

实现了类似敏感词检查的用户ID映射功能，用于`share.go`中的私密分享解密逻辑。这是一个临时过渡逻辑，专门针对kof游戏的用户ID映射需求。

## 实现方案

### 1. 封装映射逻辑

在`SensitiveLogic`中提取了公共方法`MapUserIDForKofGame`：

```go
func (l *SensitiveLogic) MapUserIDForKofGame(ctx context.Context, userID, gameID, logPrefix string) (mappedUserID string, mappedUser *model.AUserMinigame, err error)
```

### 2. 功能特点

- **游戏特定性**: 只针对`gameID == "kof"`的游戏进行映射
- **用户类型判断**: 通过检查用户在hlxq和kof游戏中的账户数量判断新老用户
- **映射策略**:
  - 如果只有一个账户：新用户，无需映射
  - 如果有多个账户：老用户，使用kof游戏中的用户ID进行映射
- **日志记录**: 详细的日志记录用于调试和监控

### 3. 集成到ShareLogic

在`ShareLogic`中添加了对`SensitiveLogic`的依赖：

```go
type ShareLogic struct {
    // ... 其他依赖
    sensitiveLogic   *SensitiveLogic // 用于调用用户ID映射逻辑
}
```

### 4. 使用示例

在`DecryptUserPrivateShare`方法中的使用：

```go
// 用户ID映射功能 - 临时过渡逻辑  
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
mappedUserID, user, err := l.sensitiveLogic.MapUserIDForKofGame(ctx, req.UserID, req.GameID, "私密分享解密")
if err != nil {
    return nil, err
}

logger.Logger.InfofCtx(ctx, "[私密分享解密] 使用UserID进行解密: %s, OpenID: %s", mappedUserID, user.OpenID)
```

## 代码位置

### 修改的文件

1. `/internal/logic/sensitive.go` - 提取公共映射方法
2. `/internal/logic/share.go` - 集成用户ID映射逻辑

### 新增的文件

1. `/internal/logic/share_test.go` - 测试文件和使用示例

## 注意事项

### 临时性质

- 这是临时过渡逻辑，带有明确的TODO注释标记
- 后续需要完全移除此功能
- 所有相关代码都有清晰的注释说明其临时性质

### 删除计划

将来删除时需要：

1. 删除`SensitiveLogic.MapUserIDForKofGame`方法
2. 删除`ShareLogic`中对`sensitiveLogic`的依赖
3. 恢复`DecryptUserPrivateShare`方法中的直接用户查询
4. 删除相关的测试代码

### 维护建议

- 定期检查此功能是否仍然需要
- 监控日志输出，确保映射逻辑正常工作
- 考虑设置功能开关，便于将来的渐进式移除

## 测试

创建了测试文件`share_test.go`，包含：

- 集成测试示例
- 逻辑正确性测试用例
- 使用示例代码

测试覆盖了：

- kof游戏的映射逻辑
- 非kof游戏的直接返回逻辑
- 错误处理场景

## 编译验证

已通过Go编译验证，确保所有更改正确无误。
