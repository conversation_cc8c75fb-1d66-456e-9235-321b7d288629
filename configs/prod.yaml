LoggerConf:
  level: info
  file_name: logs/admin-console
  max_size: 100
  max_backups: 10
  max_age: 30

CorsConf:
  allow_origin:
    - https://platform-web.bkxgame.com
    - https://platform-client.bkxgame.com
    - https://res-test-cdn.hongyiyi.xyz
    - https://trading-cdn.bkxgame.com

MysqlConf:
  host: **********
  port: 3306
  user_name: platform
  password: "@2Olps2w^&BR@4zj"
  db_name: admin-console
  open_conns: 100
  idle_conns: 50
  slow_sql_threshold: 5000

RedisConf:
  type: single
  hosts:
    -  **********:6379
  db: 0
  user_name:
  password: 2cO%>OBm2}b5$jx
  poolsize: 1000
  minidle_cons: 100

RedisTaskConf:
  type: single
  hosts:
    -  **********:6379
  db: 0
  user_name:
  password: 2cO%>OBm2}b5$jx
  poolsize: 1000
  minidle_cons: 100

ServerConf:
  jwt_secret: 6a25936cd60b49ff801f114250d366b6
  jwt_refresh_secret: l3H27T4Lv4IrTHJNKNnGitszbx21byXz

KafkaConf:
  addr:
    - ckafka-vvjv9vbx.ap-beijing.ckafka.tencentcloudmq.com:50001
  user: ckafka-vvjv9vbx#platform
  password: r@3#S5@6>w!zj
  group_name: cg-admin-console
  offsets: false

MinigameConf:
  base_url: https://api.weixin.qq.com

WechatPayConf:
  midas_env: 0
  base_url: https://api.mch.weixin.qq.com
  customer_pay_url: https://platform-client.bkxgame.com/wx-payment/index.html
  h5_pay_callback_url: https://platform.bkxgame.com/admin-console/wechat/h5/pay_callback

GravityEngineConf:
  env: 0
  base_url: https://backend.gravity-engine.com

IpRegionConf:
  file_path: ./static/ip2region.xdb

ThinkDataConf:
  think_data_app_id: 05b9d321bcdb4f7daf8fe23f29aad4cd

DouyinConf:
  base_url: https://minigame.zijieapi.com
  toutiao_url : https://developer.toutiao.com

QQConf:
  base_url: https://api.q.qq.com

QiyuConf:
  base_url: https://qiyukf.com
  app_key: 697f4043e84daf5672b527dcefaf3042
  app_secret: D1DE4D037B944458932182D99E6D0A39
  return_env: formal

OSS:
  env: qiyu
  domain: https://platform-oss-cdn.bkxgame.com
  bucket_url: https://bkxplatform-1324478747.cos.ap-beijing.myqcloud.com
  secret_id: AKIDZ3QdieR3XuDNe7eUEoPpK4oeVTTNQMTo
  secret_key: KWk4NafoQ6nhhdccffkBcvNuOxh2Ofrw

TencentCloudConf:
  secret_id: AKIDMcflrqcCcsvpkbItOjusocFguAJetLk8
  secret_key: GNOHxRjv1zfIoCNdjrsnXDsNzWxGOORN
  biz_type: 1849368708099608576
  captcha_secret_id: AKIDmRE7vZ5gEg5L0iMsPpFYL1Yv8MXYYhlf
  captcha_secret_key: 314DH9jBPfovtd8ScYPD1hP1PZjmZ9UY

NetEaseYidunConf:
  secret_id: cf06c8bb85ddf948745fef6336e96cc4
  secret_key: 1c62fcf57cf8893ae9b9ffa121076440
  business_id: 7776f85198e5c7f99b51321c5bc89ae1

MiniprogramConf:
  is_refresh_url_link: true
  env_version: "release"

AIModelConf:
  base_url: https://www.bigmodel.cn/api/paas/v4/chat/completions
  api_key: fbaf1abd68164f749d0249e8dd0719e0.momeSiAk5ZPLeAwt
  model: GLM-4-Flash-250414

SmtpConf:
  host: "smtp.feishu.cn"
  port: 465
  username: "<EMAIL>"
  password: "DwAvlMUOFMys7cKn"
  from: "系统通知 <<EMAIL>>"
  
EmailTaskConf:
  workorder_stats:
    cron_spec: "0 0 10 * * *"
    time_zone: "Asia/Shanghai"
    duration: 24h  # 统计24小时的数据

ExtraDataReportConf:
  target_game_id: "hlxq"
  target_thinking_data_app_id: "7366ccc227474c04804ba4179e42a879"

CustomerServiceConf:
  base_url: "https://platform-client.bkxgame.com/h5-kefu/#/pages/chat/index"

FaceFusionConf:
  project_id: "at_1954810067980918784"
  callback_url: "https://kof-test-gate.bkxgame.com/..."
  timeout_seconds: 10
  qps_limit: 18
  max_queue_size: 1000
  queue_timeout_seconds: 300
  enable_qps_limit: true

UserIDMappingConf:
  enabled: true
  new_game_id: "mygame"
  old_game_id: "kof-test"