package task

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/facefusion"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/go-resty/resty/v2"
	"github.com/hibiken/asynq"
	"github.com/jinzhu/copier"
)

type SchedulerCronType string // 定时语法 @every 10s * * * * *

var (
	TypeSubscribeMessage     = "subscribe_msg"
	TypeQiyuSubscribeMessage = "qiyu_subscribe_msg"
	TypeCustomerMessage      = "customer_msg"
	TypeDouyinPayCallback    = "douyin_pay_callback" // 抖音回调

	TypePayCallbackToProduct = "pay_callback_to_product"
	TypeProductShipmentOrder = "product_shipment_order"

	TypeReportCallback     = "report_callback"      // 举报系统回调
	TypeMonitorCallback    = "monitor_callback"     // 内容监控回调
	TypeDouyinGiftDelivery = "douyin_gift_delivery" // 抖音礼包推送回调
	TypeFaceFusion         = "face_fusion"          // 人脸融合处理（统一处理和回调）
	TypeFaceFusionCallback = "face_fusion_callback" // 人脸融合回调重试

	TypeWorkOrderReply = "work_order_reply" // 工单回复
)

// WorkOrderReplyTaskData 工单回复任务数据
type WorkOrderReplyTaskData struct {
	OrderID      string `json:"order_id"`
	ReplyID      int32  `json:"reply_id"`
	UserID       string `json:"user_id"`
	Username     string `json:"username"`
	Content      string `json:"content"`
	ReplyContent string `json:"reply_content"`
	ReplyTime    int64  `json:"reply_time"`
	GameID       string `json:"game_id"`
	OpenID       string `json:"open_id"`
}

func GetHandleTasks() map[string]asynq.HandlerFunc {
	return map[string]asynq.HandlerFunc{
		TypeSubscribeMessage:     HandleSubscribeMessageTask,
		TypeQiyuSubscribeMessage: HandleQiyuSubscribeMessageTask,
		TypeCustomerMessage:      HandleCustomerMessageTask,
		TypeDouyinPayCallback:    HandleDouyinPayTask,
		// TypePayCallbackToProduct: HandlePayCallbackToProductTask,
		TypeProductShipmentOrder: HandleProductShipmentOrderTask,
		TypeReportCallback:       HandleReportServiceCallbackTask,
		TypeMonitorCallback:      HandleMonitorServiceCallbackTask,
		TypeDouyinGiftDelivery:   HandleDouyinGiftDeliveryTask,
		TypeFaceFusion:           HandleFaceFusionTask,
		TypeFaceFusionCallback:   HandleFaceFusionCallbackTask,
		TypeWorkOrderReply:       HandleWorkOrderReplyTask,
	}
}

func HandleSubscribeMessageTask(ctx context.Context, t *asynq.Task) error {
	var req bean.SubscribeMessageTaskReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.Errorf("HandleSubscribeMessageTask json.Unmarshal failed: %s", err.Error())
		return err
	}

	var (
		status int32 = constants.WechatSubscribeMsgSuccessStatus
		errMsg string
	)

	// req.AccessToken 会过期
	// 根据OpenID 获取最新的AccessToken
	openID := req.OpenID
	accessToken, err := service.SingletonUserService().GetAccessTokenByOpenID(ctx, openID)
	if err != nil {
		return err
	}

	req.AccessToken = accessToken

	thirdPartyErrMsg, err := service.SingletonWechatMessageService().SubMessageNotifyWithFallback(ctx,
		req.AccessToken,
		req.OpenID,
		&bean.SubMessageNotifyReq{
			TemplateID: req.TemplateID,
			Page:       req.Page,
			Data:       req.Data,
		},
		req.OriginalUserID,
		req.OriginalGameID,
		req.IsMapped)
	if err != nil {
		// status = constants.WechatSubscribeMsgFailStatus
		return err
	}
	if thirdPartyErrMsg != "" {
		status = constants.WechatSubscribeMsgFailStatus
		errMsg = thirdPartyErrMsg
	}
	err = service.SingletonMessageService().UpdateMessageNotifyStatus(ctx, req.ID, status, errMsg)
	if err != nil {
		return err
	}
	return nil
}

// TypeQiyuSubscribeMessage
func HandleQiyuSubscribeMessageTask(ctx context.Context, t *asynq.Task) error {
	var req bean.QiyuTicketReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.Errorf("HandleQiyuSubscribeMessageTask json.Unmarshal failed: %s", err.Error())
		return err
	}

	// 读取最新的access_token
	conf, err := service.SingletonMinigameService().GetMiniprogramConfig(ctx)
	if err != nil {
		return err
	}

	_, err = service.SingletonWechatMessageService().SubMessageNotifySetReturnEnv(ctx,
		conf.AccessToken,
		req.OpenID,
		config.GlobConfig.Qiyu.ReturnEnv,
		&bean.SubMessageNotifyReq{
			TemplateID: req.TemplateID,
			Page:       req.Page,
			Data:       req.Data,
		})
	if err != nil {
		logger.Logger.Errorf("HandleQiyuSubscribeMessageTask SubMessageNotify failed: %s", err.Error())
		return err
	}
	return nil
}

func HandleCustomerMessageTask(ctx context.Context, t *asynq.Task) error {
	var req bean.CustomerMessageTaskReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.Errorf("HandleCustomerMessageTask json.Unmarshal failed: %s", err.Error())
		return err
	}
	err := service.SingletonMinigameService().SendWechatCustomerService(ctx, req.AccessToken, req.OpenID, req.MsgType, req.Data)
	if err != nil {
		logger.Logger.Errorf("HandleCustomerMessageTask SendWechatCustomerService failed: %s", err.Error())
		return err
	}
	return nil
}

// HandleDouyinPayTask 抖音支付回调处理（带防重复扣币保护）
// 处理流程：
// 1. 分布式锁保护，防止并发处理
// 2. 订单状态检查，防止重复处理
// 3. 重新获取最新的 Access Token
// 4. 验证支付完成状态
// 5. 扣币幂等性检查，防止重复扣币
// 6. 扣除用户游戏币
// 7. 提交产品发货异步任务
func HandleDouyinPayTask(ctx context.Context, t *asynq.Task) error {
	var req bean.DouyinPayTaskReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[HandleDouyinPayTask] 解析任务数据失败: %v", err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 开始处理抖音支付任务, order_id: %s, open_id: %s, is_small_diamond: %v", req.OrderID, req.OpenID, req.IsSmallDiamond)

	// 1. 添加分布式锁保护，防止同一订单的任务并发处理
	lockKey := fmt.Sprintf("douyin_pay_task:%s", req.OrderID)
	if isLock := redis.Lock(ctx, lockKey, 600*time.Second); !isLock {
		logger.Logger.WarnfCtx(ctx, "[HandleDouyinPayTask] 获取分布式锁失败，可能存在并发处理, order_id: %s", req.OrderID)
		return constants.ErrSystemServiceIsBusy
	}
	defer func() {
		redis.UnLock(ctx, lockKey)
		logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 释放分布式锁, order_id: %s", req.OrderID)
	}()

	// 2. 获取订单详情并检查状态，防止重复处理
	orderDetail, err := service.SingletonOrderService().GetOrderDetail(ctx, req.OrderID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[HandleDouyinPayTask] 获取订单详情失败, order_id: %s, error: %v", req.OrderID, err)
		return err
	}

	// 3. 订单状态检查：如果订单已处理完成，跳过处理
	if orderDetail.Status >= constants.PaymentProductShipmentSuccess {
		logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 订单已处理完成，跳过处理, order_id: %s, current_status: %d", req.OrderID, orderDetail.Status)
		return nil // 不返回错误，避免重试
	}

	logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 订单状态检查通过, order_id: %s, current_status: %d", req.OrderID, orderDetail.Status)

	// 4. 重新获取最新的抖音配置和 Access Token
	// 如果请求中有 GameID，优先使用；否则从订单中获取
	gameID := req.GameID
	if gameID == "" {
		gameID = orderDetail.GameID
	}

	douyinConf, err := service.SingletonDouyinService().GetDouyinConf(ctx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[HandleDouyinPayTask] 获取抖音配置失败, game_id: %s, error: %v", gameID, err)
		return err
	}

	// 使用最新的 Access Token 替换可能过期的 token
	req.AccessToken = douyinConf.AccessToken
	req.PaySecret = douyinConf.PaySecret
	req.AppID = douyinConf.AppID

	logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 已获取最新的 Access Token, order_id: %s", req.OrderID)

	// 5. 验证支付完成状态
	// err = service.SingletonOrderService().ProcessOrder(ctx,
	// 	req.OpenID, req.AppID, req.AccessToken, req.PaySecret, req.OrderPlatform, req.SaveAmt)
	// if err != nil {
	// 	logger.Logger.ErrorfCtx(ctx, "[HandleDouyinPayTask] 验证支付完成状态失败, order_id: %s, error: %v", req.OrderID, err)
	// 	return err
	// }

	// logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 支付完成状态验证通过, order_id: %s", req.OrderID)

	// 6. 检查是否为小额钻石支付，如果是则跳过扣币流程
	if req.IsSmallDiamond {
		logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 检测到小额钻石支付，跳过扣币流程, order_id: %s, reason: 小额钻石支付无需扣除游戏币", req.OrderID)
	} else {
		// 7. 扣币幂等性检查和扣币操作
		deductKey := fmt.Sprintf("douyin_deduct_currency:%s", req.OrderID)

		// 使用 Lock 实现原子操作，避免竞态条件
		// 这里使用 Lock 而不是普通的 Set，确保只有第一次能成功
		isFirstTime := redis.Lock(ctx, deductKey, 24*time.Hour)

		if !isFirstTime {
			// 已经存在标记，说明已经扣过币
			logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 该订单已扣过币，跳过扣币操作, order_id: %s", req.OrderID)
			// 直接进入下一步，不需要再次扣币
		} else {
			// 成功设置标记，执行扣币操作
			// 8. 扣除用户游戏币
			param := &bean.DeductGameCurrencyParam{
				OpenID:      req.OpenID,
				AppID:       req.AppID,
				Ts:          time.Now().Unix(),
				ZoneID:      "1",
				PF:          req.OrderPlatform,
				Amt:         req.DeductAmt, // 扣除游戏币数量
				BillNo:      req.OrderID,
				AccessToken: req.AccessToken,
				MpSig:       req.PaySecret,
			}

			_, err = service.SingletonDouyinService().DeductGameCurrency(ctx, param)
			if err != nil {
				// 扣币失败，删除标记以便重试
				// 注意：由于没有 Del 方法，只能通过设置过期时间为 0 来删除
				if delErr := redis.Redis().Del(ctx, deductKey).Err(); delErr != nil {
					logger.Logger.WarnfCtx(ctx, "[HandleDouyinPayTask] 删除扣币标记失败, order_id: %s, error: %v", req.OrderID, delErr)
				}
				logger.Logger.ErrorfCtx(ctx, "[HandleDouyinPayTask] 扣除游戏币失败, order_id: %s, error: %v", req.OrderID, err)
				return err
			}

			logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 扣除游戏币成功, order_id: %s, deduct_amt: %d", req.OrderID, req.DeductAmt)
		}
	}

	// 9. 提交产品发货异步任务
	logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 开始提交产品发货异步任务, order_id: %s", req.OrderID)

	err = submitProductShipmentTask(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[HandleDouyinPayTask] 提交产品发货异步任务失败, order_id: %s, error: %v", req.OrderID, err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "[HandleDouyinPayTask] 抖音支付任务处理完成, order_id: %s", req.OrderID)
	return nil
}

func validateOrderShipment(ctx context.Context, order *bean.ProductShipmentOrder) error {
	if order.PlatformType == constants.PlatformTypeIOS && order.PayType == constants.PayTypeAndroidWechatPay {
		logger.Logger.WarnfCtx(ctx, "订单疑似异常, 不发货, gameid: %s, userid: %s, orderid: %s",
			order.GameID, order.UserID, order.OrderID)
		return nil // 订单疑似异常，不发货
	}
	return nil
}

func HandleProductShipmentOrderTask(ctx context.Context, t *asynq.Task) error {
	logger.Logger.Infof("HandleCallProductShipment start %s", string(t.Payload()))
	req := &bean.OrderReq{}
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.Errorf("HandleDouyinPayTask json.Unmarshal failed: %s", err.Error())
		return err
	}

	if err := validateOrderShipment(ctx, req.Order); err != nil {
		return nil // 订单疑似异常，不发货
	}

	shipmentRes, err := service.SingletonOrderService().CallProductShipment(ctx, req)
	if err != nil {
		logger.Logger.Errorf("HandleProductShipmentOrderTask call product shipment err: %s", err.Error())
		req.Attempt++
		if req.Attempt <= len(constants.RetryDelays) {
			orderByte, err := json.Marshal(req)
			if err != nil {
				return err
			}
			logger.Logger.Errorf("尝试: %d重试，延迟时间：%v，gameid: %s，userid: %s，orderid: %s",
				req.Attempt, constants.RetryDelays[req.Attempt-1], req.Order.GameID, req.Order.UserID, req.Order.OrderID)

			if req.Attempt > 1 { // 排除10 * time.Second, 之后发送警报
				logger.Logger.ErrorWithFiled(map[string]interface{}{
					"order_id": req.Order.OrderID,
					"game_id":  req.Order.GameID,
					"extra":    "连续重试完成后，调用失败",
				}, "充值回调异常")
			}

			_, err = SubmitByDelay(asynq.NewTask(TypeProductShipmentOrder, orderByte), constants.RetryDelays[req.Attempt-1])
			if err != nil {
				logger.Logger.Errorf("HandleProductShipmentOrderTask SubmitByDelay err: %s", err.Error())
				return err
			}
		} else if req.Attempt > len(constants.RetryDelays) {
			logger.Logger.Errorf("尝试%d次后，最终调用失败，gameid: %s，userid: %s，orderid: %s，err: %s",
				req.Attempt-1, req.Order.GameID, req.Order.UserID, req.Order.OrderID, err.Error())

			logger.Logger.ErrorWithFiled(map[string]interface{}{
				"order_id": req.Order.OrderID,
				"game_id":  req.Order.GameID,
				"extra":    "连续重试最大次数后，调用失败",
			}, "充值回调异常")
			return service.SingletonOrderService().UpdateOrderStatus(ctx, req.Order.OrderID, map[string]interface{}{"status": constants.PaymentProductShipmentFail})
		}

		err = service.SingletonDataReportService().ReportProductShipment(ctx, req.Order.UserID, "", req.Order.GameID, req.PlatformAppID, req.Order.OrderID, req.Order.GoodsID, req.Order.Money, req.Order.CurrencyPrice, req.Order.PayType, shipmentRes)
		if err != nil {
			logger.Logger.Errorf("HandleProductShipmentOrderTask ReportProductShipment fail gameid: %s,userid: %s,orderid: %s,err: %s", req.Order.GameID, req.Order.UserID, req.Order.OrderID, err.Error())
		}
		return nil // 打印logger但不返回error
	}

	err = service.SingletonDataReportService().ReportProductShipment(ctx, req.Order.UserID, "", req.Order.GameID, req.PlatformAppID, req.Order.OrderID, req.Order.GoodsID, req.Order.Money, req.Order.CurrencyPrice, req.Order.PayType, shipmentRes)
	if err != nil {
		logger.Logger.Errorf("HandleProductShipmentOrderTask ReportProductShipment success gameid: %s,userid: %s,orderid: %s,err: %s", req.Order.GameID, req.Order.UserID, req.Order.OrderID, err.Error())
	}

	logger.Logger.Infof("HandleCallProductShipment done, order id: %s", shipmentRes.OrderID)
	return service.SingletonOrderService().UpdateOrderStatus(ctx, shipmentRes.OrderID, map[string]interface{}{"status": constants.PaymentProductShipmentSuccess})
}

// HandleReportServiceCallbackTask 举报系统回调
func HandleReportServiceCallbackTask(ctx context.Context, t *asynq.Task) error {
	var req bean.ReportCallbackReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		return fmt.Errorf("unmarshal task payload: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "report callback start: %+v", req)

	if err := validateRequest(&req); err != nil {
		return fmt.Errorf("validate request: %w", err)
	}

	if req.CallbackURL != "" {
		req.Attempt++
		if err := handleCallback(ctx, &req, t); err != nil {
			return err
		}
	}

	if err := updateReportStatus(ctx, &req); err != nil {
		return err
	}

	logger.Logger.InfofCtx(ctx, "report callback completed: game_id=%s, role_id=%s", req.GameID, req.ReportCallbackData.ReportedRoleID)
	return nil
}

func validateRequest(req *bean.ReportCallbackReq) error {
	if req.ReportCallbackData == nil {
		return fmt.Errorf("report callback data is nil")
	}
	return nil
}

func handleCallback(ctx context.Context, req *bean.ReportCallbackReq, t *asynq.Task) error {
	callbackData, err := json.Marshal(req.ReportCallbackData)
	if err != nil {
		return fmt.Errorf("marshal callback data: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "report callback param game_id: %s, callback data: %s", req.GameID, string(callbackData))

	resp, err := makeCallbackRequest(ctx, req.GameID, req.CallbackURL, callbackData)
	if err != nil {
		return fmt.Errorf("make callback request: %w", err)
	}

	if err := validateCallbackResponse(resp); err != nil {
		return handleRetry(req, t, err)
	}

	return nil
}

func makeCallbackRequest(ctx context.Context, gameID string, url string, data []byte) (*resty.Response, error) {
	client := resty.New().SetTimeout(10 * time.Second)
	timestamp := time.Now().Unix()
	timestampStr := strconv.FormatInt(timestamp, 10)
	secretKey := fmt.Sprintf(constants.SystemSecret, gameID)
	secretVal, err := redis.Get(ctx, secretKey)
	if err != nil {
		logger.Logger.Errorf("OrderService CallProductShipment: redis get secret, game id: %s, err: %v", gameID, err)
		return nil, err
	}
	sign := middleware.GenSign(gameID, secretVal, timestampStr, data)

	return client.R().
		SetBody(data).
		SetHeader("Content-Type", "application/json").
		SetHeader("game_id", gameID).
		SetHeader("timestamp", timestampStr).
		SetHeader("sign", sign).
		Post(url)
}

func validateCallbackResponse(resp *resty.Response) error {
	var callbackResp bean.ReprtCallbackResp
	if err := json.Unmarshal(resp.Body(), &callbackResp); err != nil {
		return fmt.Errorf("parse response body: %w", err)
	}

	if resp.StatusCode() != http.StatusOK || callbackResp.Code != 0 {
		return fmt.Errorf("callback failed: status=%d, code=%d, msg=%s",
			resp.StatusCode(), callbackResp.Code, callbackResp.Msg)
	}

	return nil
}

func handleRetry(req *bean.ReportCallbackReq, t *asynq.Task, originalErr error) error {
	logger.Logger.Errorf("callback failed: game_id=%s, role_id=%s, err=%v",
		req.GameID, req.ReportCallbackData.ReportedRoleID, originalErr)

	if req.Attempt > len(constants.RetryDelays) {
		return fmt.Errorf("max retries exceeded: %w", originalErr)
	}

	delay := constants.RetryDelays[req.Attempt-1]
	logger.Logger.Infof("scheduling retry %d/%d in %v: game_id=%s, role_id=%s",
		req.Attempt, len(constants.RetryDelays), delay,
		req.GameID, req.ReportCallbackData.ReportedRoleID)

	if _, err := SubmitByDelay(asynq.NewTask(TypeReportCallback, t.Payload()), delay); err != nil {
		return fmt.Errorf("schedule retry: %w", err)
	}

	return nil
}

func updateReportStatus(ctx context.Context, req *bean.ReportCallbackReq) error {
	err := service.SingletonReportService().UpdateReportOperationStatus(ctx, req.ReportCallbackData.OperationID)
	if err != nil {
		logger.Logger.Errorf("update status failed: game_id=%s, role_id=%s, err=%v",
			req.GameID, req.ReportCallbackData.ReportedRoleID, err)
	}
	return err
}

// HandleMonitorServiceCallbackTask 内容监控系统回调
func HandleMonitorServiceCallbackTask(ctx context.Context, t *asynq.Task) error {
	logger.Logger.InfofCtx(ctx, "HandleMonitorServiceCallbackTask start")

	var req bean.ContentCallbackReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleMonitorServiceCallbackTask unmarshal task payload: %v", err)
		return fmt.Errorf("unmarshal task payload: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "content monitor callback start: %+v", req)

	if err := validateContentRequest(&req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleMonitorServiceCallbackTask validate request: %v", err)
		return fmt.Errorf("validate request: %w", err)
	}

	if req.CallbackURL != "" {
		req.Attempt++
		if err := handleContentCallback(ctx, &req, t); err != nil {
			return err
		}
	}

	if err := updateContentStatus(ctx, &req); err != nil {
		return err
	}

	logger.Logger.InfofCtx(ctx, "content monitor callback completed: game_id=%s, content_id=%s", req.GameID, req.ContentCallbackData.ContentID)
	return nil
}

func validateContentRequest(req *bean.ContentCallbackReq) error {
	if req.ContentCallbackData == nil {
		return fmt.Errorf("content callback data is nil")
	}
	return nil
}

func handleContentCallback(ctx context.Context, req *bean.ContentCallbackReq, t *asynq.Task) error {
	callbackData, err := json.Marshal(req.ContentCallbackData)
	if err != nil {
		return fmt.Errorf("marshal callback data: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "content monitor callback param game_id: %s, callback data: %s", req.GameID, string(callbackData))

	resp, err := makeCallbackRequest(ctx, req.GameID, req.CallbackURL, callbackData)
	if err != nil {
		return fmt.Errorf("make callback request: %w", err)
	}

	if err := validateContentCallbackResponse(resp); err != nil {
		return handleContentRetry(req, t, err)
	}

	return nil
}

func validateContentCallbackResponse(resp *resty.Response) error {
	var callbackResp bean.ContentCallbackResp
	if err := json.Unmarshal(resp.Body(), &callbackResp); err != nil {
		return fmt.Errorf("parse response body: %w", err)
	}

	if resp.StatusCode() != http.StatusOK || callbackResp.Code != 0 {
		return fmt.Errorf("callback failed: status=%d, code=%d, msg=%s",
			resp.StatusCode(), callbackResp.Code, callbackResp.Msg)
	}

	return nil
}

func handleContentRetry(req *bean.ContentCallbackReq, t *asynq.Task, originalErr error) error {
	logger.Logger.Errorf("content callback failed: game_id=%s, content_id=%s, err=%v",
		req.GameID, req.ContentCallbackData.ContentID, originalErr)

	if req.Attempt > len(constants.RetryDelays) {
		return fmt.Errorf("max retries exceeded: %w", originalErr)
	}

	delay := constants.RetryDelays[req.Attempt-1]
	logger.Logger.Infof("scheduling content callback retry %d/%d in %v: game_id=%s, content_id=%s",
		req.Attempt, len(constants.RetryDelays), delay,
		req.GameID, req.ContentCallbackData.ContentID)

	if _, err := SubmitByDelay(asynq.NewTask(TypeMonitorCallback, t.Payload()), delay); err != nil {
		return fmt.Errorf("schedule retry: %w", err)
	}

	return nil
}

func updateContentStatus(ctx context.Context, req *bean.ContentCallbackReq) error {
	err := service.SingletonContentService().UpdateContentStatusByContentID(ctx, req.ContentCallbackData.ContentID)
	if err != nil {
		logger.Logger.Errorf("update content status failed: game_id=%s, content_id=%s, err=%v",
			req.GameID, req.ContentCallbackData.ContentID, err)
	}
	return err
}

// HandleWorkOrderReplyTask
func HandleWorkOrderReplyTask(ctx context.Context, t *asynq.Task) error {
	var taskData WorkOrderReplyTaskData
	if err := json.Unmarshal(t.Payload(), &taskData); err != nil {
		logger.Logger.Errorf("HandleWorkOrderReplyTask json.Unmarshal failed: %s", err.Error())
		return err
	}

	logger.Logger.Infof("HandleWorkOrderReplyTask start: %+v", taskData)

	// 读取最新的access_token
	_, err := service.SingletonMinigameService().GetMiniprogramConfig(ctx)
	if err != nil {
		logger.Logger.Errorf("HandleWorkOrderReplyTask GetMiniprogramConfig failed: %s", err.Error())
		return err
	}

	// 准备订阅消息数据
	data := map[string]interface{}{
		"thing6": map[string]interface{}{"value": "您的工单有新的回复，请点击查看"},                                     // 标题
		"thing7": map[string]interface{}{"value": truncateString(taskData.Content, 17)},                  // 工单内容
		"thing3": map[string]interface{}{"value": truncateString(taskData.ReplyContent, 17)},             // 回复内容
		"time4":  map[string]interface{}{"value": time.Unix(taskData.ReplyTime/1000, 0).Format("15:04")}, // 回复时间
	}

	// 创建订阅消息请求
	taskReq := &bean.QiyuTicketReq{
		OpenID:     taskData.OpenID,
		TemplateID: constants.DefaultQiyuTicketTemplateID,
		Page:       fmt.Sprintf("/pages/detail/detail?ticket_id=%s", taskData.OrderID),
		Data:       data,
	}

	taskReqByte, err := json.Marshal(taskReq)
	if err != nil {
		logger.Logger.Errorf("HandleWorkOrderReplyTask json.Marshal failed: %s", err.Error())
		return err
	}

	// 提交订阅消息任务
	taskID, err := Submit(asynq.NewTask(TypeQiyuSubscribeMessage, taskReqByte))
	if err != nil {
		logger.Logger.Errorf("HandleWorkOrderReplyTask Submit failed: %s", err.Error())
		return err
	}

	logger.Logger.Infof("HandleWorkOrderReplyTask submit return taskID: %s, orderID: %s, gameID: %s",
		taskID, taskData.OrderID, taskData.GameID)
	return nil
}

// 辅助函数：截断字符串（支持 Unicode）
func truncateString(s string, maxLen int) string {
	runes := []rune(s)
	if len(runes) <= maxLen {
		return s
	}
	return string(runes[:maxLen])
}

// submitProductShipmentTask 提交产品发货异步任务
func submitProductShipmentTask(ctx context.Context, req bean.DouyinPayTaskReq) error {
	logger.Logger.InfofCtx(ctx, "[submitProductShipmentTask] 开始提交产品发货异步任务, order_id: %s", req.OrderID)

	// 1. 获取订单详情
	orderDetail, err := service.SingletonOrderService().GetOrderDetail(ctx, req.OrderID)
	if err != nil {
		return fmt.Errorf("获取订单详情失败: %w", err)
	}

	// 2. 获取游戏平台配置
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	gameInfo, err := gameCtx.Where(game.GameID.Eq(orderDetail.GameID)).First()
	if err != nil {
		return fmt.Errorf("获取游戏配置失败: %w", err)
	}

	// 3. 构造回调URL（与现有系统保持一致的优先级逻辑）
	callbackURL := getCallbackURL(orderDetail.ShipmentCallback, gameInfo.PayCallback)
	if callbackURL == "" {
		logger.Logger.WarnfCtx(ctx, "[submitProductShipmentTask] 游戏未配置发货回调URL, game_id: %s, order_id: %s",
			orderDetail.GameID, req.OrderID)
		return nil // 不返回错误，避免重试
	}

	// 4. 构造产品发货订单
	productOrder := &bean.ProductShipmentOrder{}
	err = copier.Copy(productOrder, orderDetail)
	if err != nil {
		return fmt.Errorf("复制订单数据失败: %w", err)
	}

	// 5. 构造发货请求（与现有系统字段顺序保持一致）
	orderReq := &bean.OrderReq{
		Attempt:       0,
		Order:         productOrder,
		PlatformAppID: gameInfo.PlatformAppID,
		CallbackURL:   callbackURL,
	}

	// 6. 序列化任务数据
	orderByte, err := json.Marshal(orderReq)
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %w", err)
	}

	// 7. 提交异步任务
	taskID, err := Submit(asynq.NewTask(TypeProductShipmentOrder, orderByte))
	if err != nil {
		return fmt.Errorf("提交异步任务失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[submitProductShipmentTask] 产品发货异步任务提交成功, order_id: %s, task_id: %s",
		req.OrderID, taskID)

	return nil
}

// getCallbackURL 获取回调地址（与 OrderLogic.getCallbackURL 保持完全一致）
// 如自定义发货回调地址不为空，则使用自定义发货回调地址，否则使用默认发货回调地址
func getCallbackURL(shipmentCallback, defaultCallback string) string {
	if shipmentCallback != "" {
		return shipmentCallback
	}
	return defaultCallback
}

// HandleDouyinGiftDeliveryTask 抖音礼包推送任务处理
func HandleDouyinGiftDeliveryTask(ctx context.Context, t *asynq.Task) error {
	var req bean.DouyinGiftDeliveryTaskReq
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.Errorf("HandleDouyinGiftDeliveryTask json.Unmarshal failed: %s", err.Error())
		return err
	}

	// 只检查最关键的空指针
	if req.CallbackData == nil {
		logger.Logger.ErrorfCtx(ctx, "HandleDouyinGiftDeliveryTask callback data is nil: gameID=%s", req.GameID)
		return fmt.Errorf("callback data is nil")
	}

	logger.Logger.InfofCtx(ctx, "HandleDouyinGiftDeliveryTask start: gameID=%s, giftCode=%s, userID=%s, callbackURL=%s",
		req.GameID, req.CallbackData.GiftCode, req.CallbackData.UserID, req.CallbackURL)

	if req.CallbackURL != "" {
		req.Attempt++
		if err := handleGiftDeliveryCallback(ctx, &req, t); err != nil {
			return err
		}
	}

	logger.Logger.InfofCtx(ctx, "HandleDouyinGiftDeliveryTask completed: gameID=%s, giftCode=%s", req.GameID, req.CallbackData.GiftCode)
	return nil
}

// handleGiftDeliveryCallback 处理礼包推送回调
func handleGiftDeliveryCallback(ctx context.Context, req *bean.DouyinGiftDeliveryTaskReq, t *asynq.Task) error {
	// 简单的空指针检查（上层已经检查过 CallbackData，这里不重复检查）
	callbackData, err := json.Marshal(req.CallbackData)
	if err != nil {
		return fmt.Errorf("marshal callback data: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "HandleDouyinGiftDeliveryTask callback param gameID: %s, userID: %s, callback data: %s",
		req.GameID, req.CallbackData.UserID, string(callbackData))

	resp, err := makeCallbackRequest(ctx, req.GameID, req.CallbackURL, callbackData)
	if err != nil {
		return fmt.Errorf("make callback request: %w", err)
	}

	if err := validateGiftDeliveryCallbackResponse(resp); err != nil {
		return handleGiftDeliveryRetry(req, t, err)
	}

	return nil
}

// validateGiftDeliveryCallbackResponse 验证礼包推送回调响应
func validateGiftDeliveryCallbackResponse(resp *resty.Response) error {
	var callbackResp bean.DouyinCustomerServiceCallbackResp
	if err := json.Unmarshal(resp.Body(), &callbackResp); err != nil {
		return fmt.Errorf("parse response body: %w", err)
	}

	if resp.StatusCode() != http.StatusOK || !callbackResp.Success {
		errCode := int32(0)
		if callbackResp.ErrCode != nil {
			errCode = *callbackResp.ErrCode
		}
		return fmt.Errorf("callback failed: status=%d, success=%t, err_code=%d, reason=%s",
			resp.StatusCode(), callbackResp.Success, errCode, callbackResp.Reason)
	}

	return nil
}

// handleGiftDeliveryRetry 处理礼包推送重试
func handleGiftDeliveryRetry(req *bean.DouyinGiftDeliveryTaskReq, t *asynq.Task, originalErr error) error {
	logger.Logger.Errorf("gift delivery callback failed: gameID=%s, giftCode=%s, err=%v",
		req.GameID, req.CallbackData.GiftCode, originalErr)

	if req.Attempt > len(constants.RetryDelays) {
		return fmt.Errorf("max retries exceeded: %w", originalErr)
	}

	delay := constants.RetryDelays[req.Attempt-1]
	logger.Logger.Infof("scheduling gift delivery retry %d/%d in %v: gameID=%s, giftCode=%s",
		req.Attempt, len(constants.RetryDelays), delay,
		req.GameID, req.CallbackData.GiftCode)

	if _, err := SubmitByDelay(asynq.NewTask(TypeDouyinGiftDelivery, t.Payload()), delay); err != nil {
		return fmt.Errorf("schedule retry: %w", err)
	}

	return nil
}

// HandleFaceFusionTask 人脸融合任务处理 - 仅支持新任务格式
func HandleFaceFusionTask(ctx context.Context, t *asynq.Task) error {
	// 检查任务参数
	if t == nil {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionTask received nil task")
		return fmt.Errorf("task cannot be nil")
	}

	payload := t.Payload()
	if len(payload) == 0 {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionTask received empty payload")
		return fmt.Errorf("task payload cannot be empty")
	}

	// 解析新的任务请求格式
	var taskReq bean.FaceFusionTaskRequest
	if err := json.Unmarshal(payload, &taskReq); err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionTask unmarshal failed: payload_len=%d, err=%v", len(payload), err)
		return fmt.Errorf("unmarshal face fusion task request: %w", err)
	}

	// 基本字段验证
	if taskReq.TaskID == "" {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionTask task_id is empty")
		return fmt.Errorf("task_id cannot be empty")
	}

	return handleFaceFusionTaskRequest(ctx, &taskReq)
}

// handleFaceFusionTaskRequest 处理新格式的人脸融合任务请求
func handleFaceFusionTaskRequest(ctx context.Context, req *bean.FaceFusionTaskRequest) (returnErr error) {
	// 添加panic恢复机制
	defer func() {
		if r := recover(); r != nil {
			taskID := "unknown"
			if req != nil && req.TaskID != "" {
				taskID = req.TaskID
			}
			logger.Logger.ErrorfCtx(ctx, "FaceFusion task processing panic recovered: task_id=%s, panic=%v", taskID, r)
			returnErr = fmt.Errorf("task processing panic: %v", r)
		}
	}()

	// 添加nil检查保护，防止panic
	if req == nil {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionTaskRequest received nil request")
		return fmt.Errorf("request cannot be nil")
	}

	// 添加关键字段的安全检查
	taskID := "unknown"
	if req.TaskID != "" {
		taskID = req.TaskID
	}

	logger.Logger.InfofCtx(ctx, "HandleFaceFusionTaskRequest started: task_id=%s, game_id=%s, model_id=%s, user_id=%s",
		taskID, req.GameID, req.ModelID, req.UserID)

	// 参数验证
	if err := validateFaceFusionTaskRequestNew(req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionTaskRequest validation failed: task_id=%s, err=%v", taskID, err)
		return fmt.Errorf("validate face fusion request: %w", err)
	}

	// 获取全局队列实例
	queue := facefusion.GetGlobalQueue()

	// 定义任务处理器函数
	processor := func(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
		return processFaceFusionTaskDirectly(ctx, request)
	}

	// 通过队列处理任务（支持QPS限制）
	result, err := queue.SubmitWithProcessor(ctx, req, processor)
	if err != nil {
		// 安全的日志记录
		logger.Logger.ErrorfCtx(ctx, "FaceFusion queue processing failed: task_id=%s, err=%v", taskID, err)

		// 如果队列处理失败，直接处理以确保任务不会丢失
		logger.Logger.InfofCtx(ctx, "Fallback to direct processing: task_id=%s", taskID)
		result, err = processFaceFusionTaskDirectly(ctx, req)
		if err != nil {
			return err
		}
	}

	// 安全地获取状态信息
	status := "unknown"
	if result != nil {
		status = result.Status
	}
	logger.Logger.InfofCtx(ctx, "HandleFaceFusionTaskRequest completed: task_id=%s, status=%s", taskID, status)
	return nil
}

// processFaceFusionTaskDirectly 直接处理人脸融合任务的核心逻辑
func processFaceFusionTaskDirectly(ctx context.Context, req *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
	// 从配置获取ProjectID
	projectID := config.GlobConfig.FaceFusion.ProjectID

	// 设置默认值
	if req.RspImgType == "" {
		req.RspImgType = "url" // 默认返回url用于上传OSS
	}
	if req.LogoAdd == nil {
		logoAdd := int64(0)
		req.LogoAdd = &logoAdd
	}

	// 1. 创建数据库记录
	faceFusionService := service.SingletonFaceFusionService()
	_, err := faceFusionService.CreateFaceFusionRecord(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to create face fusion record: task_id=%s, err=%v", req.TaskID, err)
		return nil, fmt.Errorf("create face fusion record: %w", err)
	}

	// 构建任务结果
	result := &bean.FaceFusionTaskResult{
		TaskID:      req.TaskID,
		GameID:      req.GameID,
		UserID:      req.UserID,
		ModelID:     req.ModelID,
		ProjectID:   projectID,
		RequestID:   "",
		Status:      "failed",
		Message:     "",
		ProcessedAt: time.Now(),
	}

	// 2. 执行图片检测
	imageDetectionService := service.SingletonImageDetectionService()
	if imageDetectionService == nil {
		result.Message = "Image detection service is not available"
		logger.Logger.ErrorfCtx(ctx, "Image detection service is nil: task_id=%s", req.TaskID)
		return result, nil
	}

	if err := imageDetectionService.DetectImagesInMergeInfos(ctx, req.MergeInfos); err != nil {
		result.Message = fmt.Sprintf("Image detection failed: %v", err)
		logger.Logger.ErrorfCtx(ctx, "Image detection failed: task_id=%s, err=%v", req.TaskID, err)

		// 更新数据库记录为失败状态
		if updateErr := faceFusionService.UpdateFaceFusionResult(ctx, req.TaskID, result); updateErr != nil {
			logger.Logger.ErrorfCtx(ctx, "Failed to update face fusion record after image detection failure: task_id=%s, err=%v", req.TaskID, updateErr)
		}

		// 发送失败回调
		if callbackErr := sendFaceFusionTaskCallbackNew(ctx, result); callbackErr != nil {
			logger.Logger.ErrorfCtx(ctx, "Failed to send failure callback: task_id=%s, err=%v", req.TaskID, callbackErr)
		}
		return result, nil // 返回失败结果，不返回错误
	}

	// 3. 执行人脸融合
	legacyReq := &bean.FaceFusionReq{
		GameID:     req.GameID,
		UserID:     req.UserID,
		ModelID:    req.ModelID,
		MergeInfos: req.MergeInfos,
		// FuseFaceDegree:    req.FuseFaceDegree,
		// FuseProfileDegree: req.FuseProfileDegree,
		// LogoParam:         req.LogoParam,
		// FuseParam:         req.FuseParam,
		LogoAdd:    req.LogoAdd,
		RspImgType: req.RspImgType,
		TaskID:     req.TaskID,
	}

	tencentCloudService := service.SingletonTencentCloudService()
	resp, err := tencentCloudService.FaceFusion(ctx, legacyReq)

	if err != nil {
		result.Message = err.Error()
		logger.Logger.ErrorfCtx(ctx, "FaceFusion processing failed: task_id=%s, err=%v", req.TaskID, err)
	} else {
		result.Status = "success"
		result.FusedImage = resp.FusedImage
		result.RequestID = resp.RequestID
		result.Message = "Face fusion completed successfully"
		logger.Logger.InfofCtx(ctx, "FaceFusion processing completed: task_id=%s, request_id=%s", req.TaskID, resp.RequestID)
	}

	// 4. 更新数据库记录（包括OSS上传）
	var finalOssURL string
	if updateErr := faceFusionService.UpdateFaceFusionResult(ctx, req.TaskID, result); updateErr != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to update face fusion record (including OSS upload): task_id=%s, err=%v", req.TaskID, updateErr)
		// 如果更新失败，检查是否是OSS上传导致的，如果是则发送失败回调
		if result.Status == "success" {
			// 如果原本是成功状态但更新失败，可能是OSS上传问题，修改状态为失败
			result.Status = "failed"
			result.Message = fmt.Sprintf("Face fusion completed but database update failed: %v", updateErr)
		}
		// 继续发送回调，确保调用方知道处理结果
	} else {
		// 更新成功后，从数据库重新获取OSS URL用于回调
		if result.Status == "success" {
			record, err := faceFusionService.GetFaceFusionByTaskID(ctx, req.TaskID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "Failed to get updated record for callback: task_id=%s, err=%v", req.TaskID, err)
			} else if record != nil && record.OssURL != "" {
				// 保存OSS URL用于回调
				finalOssURL = record.OssURL
				logger.Logger.InfofCtx(ctx, "Retrieved OSS URL for callback: task_id=%s, oss_url=%s", req.TaskID, finalOssURL)
			}
		}
	}

	// 5. 准备回调数据 - 确保使用正确的图片URL
	if result.Status == "success" && finalOssURL != "" {
		// 使用OSS URL作为回调中的融合图片URL
		result.FusedImage = finalOssURL
	}

	// 6. 发送回调
	if callbackErr := sendFaceFusionTaskCallbackNew(ctx, result); callbackErr != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to send callback: task_id=%s, err=%v", req.TaskID, callbackErr)
	}

	return result, nil
}

// validateFaceFusionTaskRequestNew 验证新格式的人脸融合任务请求参数
func validateFaceFusionTaskRequestNew(req *bean.FaceFusionTaskRequest) error {
	if req.GameID == "" {
		return fmt.Errorf("game_id is required")
	}
	if req.ModelID == "" {
		return fmt.Errorf("model_id is required")
	}
	if req.UserID == "" {
		return fmt.Errorf("user_id is required")
	}
	if len(req.MergeInfos) == 0 {
		return fmt.Errorf("merge_infos is required")
	}
	for i, item := range req.MergeInfos {
		url, exists := item["Url"]
		if !exists {
			return fmt.Errorf("invalid merge_infos format: item %d missing 'Url' field", i)
		}
		if url == "" {
			return fmt.Errorf("invalid merge_infos format: item %d 'Url' cannot be empty", i)
		}
	}
	return nil
}

// sendFaceFusionTaskCallbackNew 发送新格式的人脸融合任务回调
func sendFaceFusionTaskCallbackNew(ctx context.Context, result *bean.FaceFusionTaskResult) error {
	callbackURL := config.GlobConfig.FaceFusion.CallbackURL
	if callbackURL == "" {
		logger.Logger.InfofCtx(ctx, "No callback URL configured, skipping callback: task_id=%s", result.TaskID)
		return nil
	}

	logger.Logger.InfofCtx(ctx, "Sending face fusion callback: task_id=%s, game_id=%s, status=%s",
		result.TaskID, result.GameID, result.Status)

	// 创建回调任务，使用重试机制
	callbackTask := &bean.FaceFusionCallbackTask{
		Attempt:     1, // 初始重试次数为1
		GameID:      result.GameID,
		CallbackURL: callbackURL,
		Result:      result,
		ScheduledAt: time.Now(),
	}

	// 序列化回调任务
	taskData, err := json.Marshal(callbackTask)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to marshal callback task: task_id=%s, err=%v", result.TaskID, err)
		return fmt.Errorf("marshal callback task: %w", err)
	}

	// 提交回调任务
	_, err = Submit(asynq.NewTask(TypeFaceFusionCallback, taskData))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to submit callback task: task_id=%s, err=%v", result.TaskID, err)
		return fmt.Errorf("submit callback task: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "Face fusion callback task submitted successfully: task_id=%s, game_id=%s", result.TaskID, result.GameID)
	return nil
}

// HandleFaceFusionCallbackTask 处理人脸融合回调重试任务
func HandleFaceFusionCallbackTask(ctx context.Context, t *asynq.Task) error {
	var req bean.FaceFusionCallbackTask
	if err := json.Unmarshal(t.Payload(), &req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleFaceFusionCallbackTask unmarshal failed: %v", err)
		return fmt.Errorf("unmarshal face fusion callback task: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "Face fusion callback task started: task_id=%s, game_id=%s, attempt=%d",
		req.Result.TaskID, req.GameID, req.Attempt)

	// 验证回调任务参数
	if err := validateFaceFusionCallbackTask(&req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "Face fusion callback task validation failed: %v", err)
		return fmt.Errorf("validate face fusion callback task: %w", err)
	}

	// 执行回调
	if err := handleFaceFusionCallback(ctx, &req, t); err != nil {
		return err
	}

	logger.Logger.InfofCtx(ctx, "Face fusion callback task completed: task_id=%s, game_id=%s", req.Result.TaskID, req.GameID)
	return nil
}

// validateFaceFusionCallbackTask 验证人脸融合回调任务参数
func validateFaceFusionCallbackTask(req *bean.FaceFusionCallbackTask) error {
	if req.Result == nil {
		return fmt.Errorf("face fusion callback result is nil")
	}
	if req.GameID == "" {
		return fmt.Errorf("game_id is required")
	}
	if req.CallbackURL == "" {
		return fmt.Errorf("callback_url is required")
	}
	if req.Result.TaskID == "" {
		return fmt.Errorf("task_id is required")
	}
	return nil
}

// handleFaceFusionCallback 处理人脸融合回调
func handleFaceFusionCallback(ctx context.Context, req *bean.FaceFusionCallbackTask, t *asynq.Task) error {
	// 序列化回调数据
	callbackData, err := json.Marshal(req.Result)
	if err != nil {
		return fmt.Errorf("marshal callback data: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "Face fusion callback param game_id: %s, task_id: %s, callback data: %s",
		req.GameID, req.Result.TaskID, string(callbackData))

	// 发送回调请求
	resp, err := makeCallbackRequest(ctx, req.GameID, req.CallbackURL, callbackData)
	if err != nil {
		return fmt.Errorf("make callback request: %w", err)
	}

	// 验证回调响应
	if err := validateFaceFusionCallbackResponse(resp); err != nil {
		return handleFaceFusionRetry(req, t, err)
	}

	return nil
}

// validateFaceFusionCallbackResponse 验证人脸融合回调响应
func validateFaceFusionCallbackResponse(resp *resty.Response) error {
	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("callback returned non-200 status: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}

	// 尝试解析响应体
	var callbackResp bean.FaceFusionCallbackResp
	if err := json.Unmarshal(resp.Body(), &callbackResp); err != nil {
		// 如果无法解析为标准格式，检查是否为简单的成功响应
		logger.Logger.Warnf("Failed to parse callback response as standard format, treating as success: %s", string(resp.Body()))
		return nil
	}

	// 检查业务状态码
	if callbackResp.Code != 0 {
		return fmt.Errorf("callback business error: code=%d, msg=%s", callbackResp.Code, callbackResp.Msg)
	}

	return nil
}

// handleFaceFusionRetry 处理人脸融合回调重试
func handleFaceFusionRetry(req *bean.FaceFusionCallbackTask, t *asynq.Task, originalErr error) error {
	logger.Logger.Errorf("Face fusion callback failed: game_id=%s, task_id=%s, attempt=%d, err=%v",
		req.GameID, req.Result.TaskID, req.Attempt, originalErr)

	if req.Attempt > len(constants.RetryDelays) {
		return fmt.Errorf("max retries exceeded: %w", originalErr)
	}

	delay := constants.RetryDelays[req.Attempt-1]
	logger.Logger.Infof("Scheduling face fusion callback retry %d/%d in %v: game_id=%s, task_id=%s",
		req.Attempt, len(constants.RetryDelays), delay,
		req.GameID, req.Result.TaskID)

	// 增加重试次数
	req.Attempt++

	// 序列化更新后的任务数据
	retryPayload, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("marshal retry payload: %w", err)
	}

	// 提交延迟重试任务
	if _, err := SubmitByDelay(asynq.NewTask(TypeFaceFusionCallback, retryPayload), delay); err != nil {
		return fmt.Errorf("schedule retry: %w", err)
	}

	return nil
}
